# Server Configuration
PORT=3001
NODE_ENV=development

# Blockchain Configuration
RPC_URL=http://127.0.0.1:8545
CHAIN_ID=9000
CONTRACT_ADDRESS=0xF19A2d609770c473a38b0547217ec60FF0AeF0aB
PRIVATE_KEY=your_private_key_here

# Payment Processing
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security
JWT_SECRET=your_jwt_secret_here
ADMIN_API_KEY=your_admin_api_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Database (if using)
# DATABASE_URL=postgresql://user:password@localhost:5432/datacoin

# Email (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password
