const express = require('express');
const { body, validationResult } = require('express-validator');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { v4: uuidv4 } = require('uuid');

const logger = require('../utils/logger');
const blockchainService = require('../utils/blockchain');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Validation middleware
const validatePurchaseRequest = [
  body('amount')
    .isFloat({ min: 1 })
    .withMessage('Amount must be at least 1 DTC'),
  body('walletAddress')
    .custom(async (value) => {
      if (!await blockchainService.validateAddress(value)) {
        throw new Error('Invalid wallet address');
      }
      return true;
    }),
  body('paymentMethodId')
    .notEmpty()
    .withMessage('Payment method ID is required'),
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
];

// Create payment intent
router.post('/create-payment-intent', validatePurchaseRequest, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { amount, walletAddress, email } = req.body;

    // Calculate price (1 DTC = $1.00 for demo)
    const priceInCents = Math.round(amount * 100);

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: priceInCents,
      currency: 'usd',
      metadata: {
        tokenAmount: amount.toString(),
        walletAddress: walletAddress,
        email: email,
        orderId: uuidv4()
      },
      description: `Purchase ${amount} DTC tokens`
    });

    logger.info(`Payment intent created: ${paymentIntent.id} for ${amount} DTC`);

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: amount,
        priceUsd: priceInCents / 100
      }
    });

  } catch (error) {
    next(error);
  }
});

// Confirm payment and mint tokens
router.post('/confirm-payment', async (req, res, next) => {
  try {
    const { paymentIntentId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        error: 'Payment intent ID is required'
      });
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        success: false,
        error: 'Payment not completed'
      });
    }

    const { tokenAmount, walletAddress, orderId } = paymentIntent.metadata;

    // Check if tokens were already minted for this payment
    // In production, you'd check this against a database
    logger.info(`Processing token mint for order ${orderId}`);

    // Mint tokens
    const mintResult = await blockchainService.mintTokens(walletAddress, tokenAmount);

    logger.info(`Tokens minted successfully for order ${orderId}: ${mintResult.transactionHash}`);

    res.json({
      success: true,
      data: {
        orderId: orderId,
        tokenAmount: tokenAmount,
        walletAddress: walletAddress,
        transactionHash: mintResult.transactionHash,
        blockNumber: mintResult.blockNumber
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get payment status
router.get('/status/:paymentIntentId', async (req, res, next) => {
  try {
    const { paymentIntentId } = req.params;

    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    res.json({
      success: true,
      data: {
        status: paymentIntent.status,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        metadata: paymentIntent.metadata
      }
    });

  } catch (error) {
    next(error);
  }
});

// Webhook endpoint for Stripe events (must be before other middleware)
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res, next) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    logger.error(`Webhook signature verification failed: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        logger.info(`Payment succeeded: ${paymentIntent.id}`);

        // Auto-mint tokens when payment succeeds
        const { tokenAmount, walletAddress, orderId } = paymentIntent.metadata;
        if (tokenAmount && walletAddress) {
          try {
            const mintResult = await blockchainService.mintTokens(walletAddress, tokenAmount);
            logger.info(`Auto-minted tokens for payment ${paymentIntent.id}: ${mintResult.transactionHash}`);
          } catch (mintError) {
            logger.error(`Failed to auto-mint tokens for payment ${paymentIntent.id}:`, mintError);
          }
        }
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        logger.warn(`Payment failed: ${failedPayment.id}`);
        break;

      default:
        logger.info(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    next(error);
  }
});

// Get purchase history (requires authentication)
router.get('/history', authenticateToken, async (req, res, next) => {
  try {
    const { walletAddress } = req.query;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required'
      });
    }

    // Get transaction history from blockchain
    const transactions = await blockchainService.getTransactionHistory(walletAddress);

    // Filter for mint transactions (purchases)
    const purchases = transactions.filter(tx =>
      tx.from === '******************************************' &&
      tx.to.toLowerCase() === walletAddress.toLowerCase()
    );

    res.json({
      success: true,
      data: {
        purchases: purchases,
        totalPurchased: purchases.reduce((sum, tx) => sum + parseFloat(tx.amount), 0)
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
