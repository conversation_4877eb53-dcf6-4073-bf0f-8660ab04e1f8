const express = require('express');
const { query, validationResult } = require('express-validator');

const logger = require('../utils/logger');
const blockchainService = require('../utils/blockchain');

const router = express.Router();

// Get token information
router.get('/info', async (req, res, next) => {
  try {
    const totalSupply = await blockchainService.getTotalSupply();
    const networkInfo = await blockchainService.getNetworkInfo();

    res.json({
      success: true,
      data: {
        name: 'DATACOIN',
        symbol: 'DTC',
        decimals: 18,
        totalSupply: totalSupply,
        contractAddress: process.env.CONTRACT_ADDRESS,
        network: networkInfo
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get balance for an address
router.get('/balance/:address', async (req, res, next) => {
  try {
    const { address } = req.params;

    if (!await blockchainService.validateAddress(address)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid address format'
      });
    }

    const balance = await blockchainService.getBalance(address);

    res.json({
      success: true,
      data: {
        address: address,
        balance: balance,
        balanceWei: balance * Math.pow(10, 18)
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get transaction history for an address
router.get('/transactions/:address', [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { address } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    if (!await blockchainService.validateAddress(address)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid address format'
      });
    }

    const allTransactions = await blockchainService.getTransactionHistory(address);
    
    // Apply pagination
    const paginatedTransactions = allTransactions.slice(offset, offset + limit);

    res.json({
      success: true,
      data: {
        address: address,
        transactions: paginatedTransactions,
        pagination: {
          total: allTransactions.length,
          limit: limit,
          offset: offset,
          hasMore: offset + limit < allTransactions.length
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get token statistics
router.get('/stats', async (req, res, next) => {
  try {
    const totalSupply = await blockchainService.getTotalSupply();
    
    // In a real implementation, you'd track these metrics in a database
    const stats = {
      totalSupply: totalSupply,
      circulatingSupply: totalSupply, // Assuming all minted tokens are in circulation
      holders: 0, // Would need to track unique addresses
      totalTransfers: 0, // Would need to count all transfer events
      marketCap: parseFloat(totalSupply) * 1.0, // Assuming $1 per token
      price: 1.0 // Fixed price for demo
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    next(error);
  }
});

// Validate an address
router.get('/validate/:address', async (req, res, next) => {
  try {
    const { address } = req.params;
    const isValid = await blockchainService.validateAddress(address);

    res.json({
      success: true,
      data: {
        address: address,
        isValid: isValid
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get network information
router.get('/network', async (req, res, next) => {
  try {
    const networkInfo = await blockchainService.getNetworkInfo();

    res.json({
      success: true,
      data: networkInfo
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
