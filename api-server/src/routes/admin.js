const express = require('express');
const { body, validationResult } = require('express-validator');

const logger = require('../utils/logger');
const blockchainService = require('../utils/blockchain');
const { authenticateAdmin } = require('../middleware/auth');

const router = express.Router();

// Apply admin authentication to all routes
router.use(authenticateAdmin);

// Validation middleware for mint/burn operations
const validateTokenOperation = [
  body('address')
    .custom(async (value) => {
      if (!await blockchainService.validateAddress(value)) {
        throw new Error('Invalid wallet address');
      }
      return true;
    }),
  body('amount')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),
  body('reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Reason must be less than 500 characters')
];

// Mint tokens
router.post('/mint', validateTokenOperation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { address, amount, reason } = req.body;

    logger.info(`Admin mint request: ${amount} DTC to ${address}. Reason: ${reason || 'Not specified'}`);

    const result = await blockchainService.mintTokens(address, amount);

    // Log the admin action
    logger.info(`Admin minted ${amount} DTC to ${address}. TX: ${result.transactionHash}`);

    res.json({
      success: true,
      data: {
        operation: 'mint',
        address: address,
        amount: amount,
        reason: reason,
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber,
        gasUsed: result.gasUsed
      }
    });

  } catch (error) {
    next(error);
  }
});

// Burn tokens from admin wallet
router.post('/burn', [
  body('amount')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),
  body('reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Reason must be less than 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { amount, reason } = req.body;

    logger.info(`Admin burn request: ${amount} DTC. Reason: ${reason || 'Not specified'}`);

    const result = await blockchainService.burnTokens(amount);

    // Log the admin action
    logger.info(`Admin burned ${amount} DTC. TX: ${result.transactionHash}`);

    res.json({
      success: true,
      data: {
        operation: 'burn',
        amount: amount,
        reason: reason,
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber,
        gasUsed: result.gasUsed
      }
    });

  } catch (error) {
    next(error);
  }
});

// Burn tokens from specific address (requires approval)
router.post('/burn-from', validateTokenOperation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { address, amount, reason } = req.body;

    logger.info(`Admin burn-from request: ${amount} DTC from ${address}. Reason: ${reason || 'Not specified'}`);

    const result = await blockchainService.burnFromAddress(address, amount);

    // Log the admin action
    logger.info(`Admin burned ${amount} DTC from ${address}. TX: ${result.transactionHash}`);

    res.json({
      success: true,
      data: {
        operation: 'burn-from',
        address: address,
        amount: amount,
        reason: reason,
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber,
        gasUsed: result.gasUsed
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get admin dashboard data
router.get('/dashboard', async (req, res, next) => {
  try {
    const totalSupply = await blockchainService.getTotalSupply();
    const networkInfo = await blockchainService.getNetworkInfo();

    // In a real implementation, you'd track these metrics in a database
    const dashboardData = {
      tokenInfo: {
        totalSupply: totalSupply,
        contractAddress: process.env.CONTRACT_ADDRESS,
        network: networkInfo
      },
      statistics: {
        totalHolders: 0, // Would need to track unique addresses
        totalTransfers: 0, // Would need to count all transfer events
        totalMinted: totalSupply, // Assuming all supply was minted
        totalBurned: 0 // Would need to track burn events
      },
      recentActivity: [] // Would fetch recent admin actions from database
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    next(error);
  }
});

// Get system health
router.get('/health', async (req, res, next) => {
  try {
    const networkInfo = await blockchainService.getNetworkInfo();
    
    const health = {
      blockchain: {
        connected: true,
        chainId: networkInfo.chainId,
        blockNumber: networkInfo.blockNumber
      },
      contract: {
        address: process.env.CONTRACT_ADDRESS,
        accessible: true
      },
      api: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      }
    };

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    // Even if blockchain is down, return partial health info
    res.status(503).json({
      success: false,
      error: 'System health check failed',
      data: {
        blockchain: {
          connected: false,
          error: error.message
        },
        api: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version
        }
      }
    });
  }
});

// Emergency stop (if implemented in contract)
router.post('/emergency-stop', [
  body('reason')
    .notEmpty()
    .withMessage('Reason is required for emergency stop')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { reason } = req.body;

    logger.warn(`EMERGENCY STOP requested. Reason: ${reason}`);

    // This would call an emergency stop function if implemented in the contract
    // For now, just log the request
    
    res.json({
      success: true,
      message: 'Emergency stop request logged',
      data: {
        timestamp: new Date().toISOString(),
        reason: reason
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
