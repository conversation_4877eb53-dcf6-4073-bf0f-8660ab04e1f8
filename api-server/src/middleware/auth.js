const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      logger.warn(`Invalid token attempt from ${req.ip}`);
      return res.status(403).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    req.user = user;
    next();
  });
};

// Middleware to verify admin API key
const authenticateAdmin = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({
      success: false,
      error: 'Admin API key required'
    });
  }

  if (apiKey !== process.env.ADMIN_API_KEY) {
    logger.warn(`Invalid admin API key attempt from ${req.ip}`);
    return res.status(403).json({
      success: false,
      error: 'Invalid admin API key'
    });
  }

  next();
};

// Middleware to verify admin role
const requireAdmin = (req, res, next) => {
  if (!req.user || !req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  authenticateAdmin,
  requireAdmin
};
