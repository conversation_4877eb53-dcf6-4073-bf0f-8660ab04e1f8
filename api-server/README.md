# DATACOIN API Server

A comprehensive Node.js API server for DATACOIN token management, payment processing, and blockchain integration.

## Features

- 🔐 **Authentication & Authorization**: JWT-based auth with admin role management
- 💳 **Payment Processing**: Stripe integration for credit card payments
- 🪙 **Token Management**: Mint, burn, and transfer DTC tokens
- 📊 **Analytics**: Token statistics and transaction history
- 🛡️ **Security**: Rate limiting, input validation, and secure headers
- 📝 **Logging**: Comprehensive logging with Winston
- 🔗 **Blockchain Integration**: Direct interaction with DATACOIN smart contract

## Quick Start

### Prerequisites

- Node.js 16+ 
- npm or yarn
- Running Ethermint node (http://127.0.0.1:8545)
- Deployed DATACOIN contract
- Stripe account (for payments)

### Installation

1. **Clone and install dependencies:**
```bash
cd api-server
npm install
```

2. **Environment setup:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start the server:**
```bash
# Development
npm run dev

# Production
npm start
```

The server will start on `http://localhost:3001`

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port | No (default: 3001) |
| `NODE_ENV` | Environment (development/production) | No |
| `RPC_URL` | Blockchain RPC endpoint | Yes |
| `CHAIN_ID` | Blockchain chain ID | Yes |
| `CONTRACT_ADDRESS` | DATACOIN contract address | Yes |
| `PRIVATE_KEY` | Admin wallet private key | Yes |
| `STRIPE_SECRET_KEY` | Stripe secret key | Yes |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook secret | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `ADMIN_API_KEY` | Admin API key | Yes |

## API Endpoints

### Authentication (`/api/auth`)

- `POST /register` - Register new user
- `POST /login` - User login
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `GET /verify` - Verify token
- `POST /logout` - User logout

### Payment (`/api/payment`)

- `POST /create-payment-intent` - Create Stripe payment intent
- `POST /confirm-payment` - Confirm payment and mint tokens
- `GET /status/:paymentIntentId` - Get payment status
- `POST /webhook` - Stripe webhook endpoint
- `GET /history` - Get purchase history

### Token (`/api/token`)

- `GET /info` - Get token information
- `GET /balance/:address` - Get token balance
- `GET /transactions/:address` - Get transaction history
- `GET /stats` - Get token statistics
- `GET /validate/:address` - Validate address
- `GET /network` - Get network information

### Admin (`/api/admin`)

- `POST /mint` - Mint tokens (admin only)
- `POST /burn` - Burn tokens (admin only)
- `POST /burn-from` - Burn from address (admin only)
- `GET /dashboard` - Get admin dashboard data
- `GET /health` - System health check
- `POST /emergency-stop` - Emergency stop (admin only)

## Usage Examples

### Purchase Tokens

```javascript
// 1. Create payment intent
const response = await fetch('/api/payment/create-payment-intent', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    amount: 100,
    walletAddress: '0x...',
    email: '<EMAIL>'
  })
});

// 2. Process payment with Stripe
// ... Stripe payment flow ...

// 3. Confirm payment and mint tokens
await fetch('/api/payment/confirm-payment', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    paymentIntentId: 'pi_...'
  })
});
```

### Admin Operations

```javascript
// Mint tokens (admin only)
await fetch('/api/admin/mint', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-admin-api-key'
  },
  body: JSON.stringify({
    address: '0x...',
    amount: 1000,
    reason: 'Token distribution'
  })
});
```

### Get Token Information

```javascript
// Get token stats
const stats = await fetch('/api/token/stats');

// Get balance
const balance = await fetch('/api/token/balance/0x...');

// Get transaction history
const history = await fetch('/api/token/transactions/0x...?limit=50');
```

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Comprehensive validation using Joi and express-validator
- **Secure Headers**: Helmet.js for security headers
- **CORS**: Configurable CORS policy
- **JWT Authentication**: Secure token-based authentication
- **Admin API Keys**: Separate authentication for admin operations

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "error": "Error message",
  "details": [] // Optional validation details
}
```

## Logging

Logs are written to:
- `logs/combined.log` - All logs
- `logs/error.log` - Error logs only
- Console (development only)

## Testing

```bash
npm test
```

## Production Deployment

1. Set `NODE_ENV=production`
2. Configure proper CORS origins
3. Use environment variables for secrets
4. Set up log rotation
5. Configure reverse proxy (nginx)
6. Set up SSL/TLS certificates
7. Configure monitoring and alerts

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
