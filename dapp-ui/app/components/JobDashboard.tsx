import React, { useState } from 'react';
import { ethers } from 'ethers';
import { Card } from './ui/Card';
import { Button } from './ui/Button';
import { LoadingSpinner } from './ui/LoadingSpinner';
import { JobCard } from './JobCard';
import { CreateJobForm } from './CreateJobForm';
import { useJobRewardSystem } from '../hooks/useJobRewardSystem';

interface JobDashboardProps {
  provider: ethers.BrowserProvider | null;
  account: string | null;
}

export const JobDashboard: React.FC<JobDashboardProps> = ({ provider, account }) => {
  const [activeTab, setActiveTab] = useState<'browse' | 'create' | 'my-jobs'>('browse');
  
  const {
    jobs,
    systemStats,
    userStats,
    dtcBalance,
    dtcAllowance,
    isLoading,
    error,
    createJob,
    assignJob,
    startJob,
    completeJob,
    cancelJob,
    approveDTC,
    loadData,
    clearError
  } = useJobRewardSystem(provider, account);

  if (!provider || !account) {
    return (
      <Card className="p-8 text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Job Reward System</h2>
        <p className="text-gray-600 mb-4">Connect your wallet to access the job marketplace</p>
        <div className="text-sm text-gray-500">
          Earn DTC tokens by completing jobs or create jobs for others to complete
        </div>
      </Card>
    );
  }

  const tabs = [
    { id: 'browse', label: 'Browse Jobs', count: jobs.length },
    { id: 'create', label: 'Create Job', count: null },
    { id: 'my-jobs', label: 'My Activity', count: userStats ? Number(userStats.jobsCreated + userStats.jobsAssigned) : 0 }
  ] as const;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Job Reward System</h1>
        <p className="text-blue-100">
          Create jobs, complete tasks, and earn DTC tokens in a decentralized marketplace
        </p>
      </div>

      {/* System Stats */}
      {systemStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {systemStats.totalJobs.toString()}
            </div>
            <div className="text-sm text-gray-600">Total Jobs</div>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {ethers.formatEther(systemStats.totalRewards)} DTC
            </div>
            <div className="text-sm text-gray-600">Total Rewards Paid</div>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {ethers.formatEther(dtcBalance)} DTC
            </div>
            <div className="text-sm text-gray-600">Your Balance</div>
          </Card>
          <Card className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              {userStats ? ethers.formatEther(userStats.totalEarned) : '0'} DTC
            </div>
            <div className="text-sm text-gray-600">Total Earned</div>
          </Card>
        </div>
      )}

      {/* User Stats */}
      {userStats && (
        <Card className="p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Your Activity</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-700">Jobs Created</div>
              <div className="text-lg font-semibold text-blue-600">{userStats.jobsCreated.toString()}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Jobs Assigned</div>
              <div className="text-lg font-semibold text-green-600">{userStats.jobsAssigned.toString()}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Total Spent</div>
              <div className="text-lg font-semibold text-red-600">{ethers.formatEther(userStats.totalSpent)} DTC</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Total Earned</div>
              <div className="text-lg font-semibold text-green-600">{ethers.formatEther(userStats.totalEarned)} DTC</div>
            </div>
          </div>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">{error}</div>
              <div className="mt-3">
                <Button
                  onClick={clearError}
                  className="bg-red-600 hover:bg-red-700 text-white text-xs"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count !== null && (
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'browse' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Available Jobs</h2>
              <Button
                onClick={loadData}
                disabled={isLoading}
                variant="outline"
                className="text-sm"
              >
                {isLoading ? <LoadingSpinner size="sm" /> : 'Refresh'}
              </Button>
            </div>
            
            {isLoading && jobs.length === 0 ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : jobs.length === 0 ? (
              <Card className="p-8 text-center">
                <div className="text-gray-500">No jobs available</div>
                <div className="text-sm text-gray-400 mt-1">
                  Be the first to create a job!
                </div>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {jobs.map((job) => (
                  <JobCard
                    key={job.jobId.toString()}
                    job={job}
                    currentUser={account}
                    onAssign={assignJob}
                    onStart={startJob}
                    onComplete={completeJob}
                    onCancel={cancelJob}
                    isLoading={isLoading}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'create' && systemStats && (
          <CreateJobForm
            onCreateJob={createJob}
            isLoading={isLoading}
            dtcBalance={dtcBalance}
            dtcAllowance={dtcAllowance}
            minimumReward={systemStats.minReward}
            onApprove={approveDTC}
          />
        )}

        {activeTab === 'my-jobs' && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">My Job Activity</h2>
            <div className="text-center py-8 text-gray-500">
              <div>My Jobs feature coming soon!</div>
              <div className="text-sm text-gray-400 mt-1">
                This will show jobs you've created and jobs assigned to you
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
