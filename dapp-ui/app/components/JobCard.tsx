import React, { useState } from 'react';
import { ethers } from 'ethers';
import { Card } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { JobCardProps } from '../types/job';
import { JOB_STATUS, JOB_STATUS_LABELS, JOB_STATUS_COLORS } from '../config/contracts';

export const JobCard: React.FC<JobCardProps> = ({
  job,
  currentUser,
  onAssign,
  onStart,
  onComplete,
  onCancel,
  isLoading = false
}) => {
  const [assigneeAddress, setAssigneeAddress] = useState('');
  const [showAssignInput, setShowAssignInput] = useState(false);

  const isCreator = currentUser?.toLowerCase() === job.creator.toLowerCase();
  const isAssignee = currentUser?.toLowerCase() === job.assignee.toLowerCase();
  const canAssign = isCreator && job.status === JOB_STATUS.CREATED;
  const canStart = isAssignee && job.status === JOB_STATUS.ASSIGNED;
  const canComplete = isAssignee && job.status === JOB_STATUS.IN_PROGRESS;
  const canCancel = isCreator && (job.status === JOB_STATUS.CREATED || job.status === JOB_STATUS.ASSIGNED);

  const handleAssign = () => {
    if (!assigneeAddress || !ethers.isAddress(assigneeAddress)) {
      alert('Please enter a valid Ethereum address');
      return;
    }
    onAssign?.(job.jobId, assigneeAddress);
    setAssigneeAddress('');
    setShowAssignInput(false);
  };

  const formatDate = (timestamp: bigint) => {
    if (timestamp === 0n) return 'N/A';
    return new Date(Number(timestamp) * 1000).toLocaleString();
  };

  const formatAddress = (address: string) => {
    if (!address || address === ethers.ZeroAddress) return 'Unassigned';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <Card className="p-6 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Job #{job.jobId.toString()}
          </h3>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${JOB_STATUS_COLORS[job.status]}`}>
            {JOB_STATUS_LABELS[job.status]}
          </span>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-green-600">
            {ethers.formatEther(job.reward)} DTC
          </div>
          <div className="text-sm text-gray-500">Reward</div>
        </div>
      </div>

      {/* Description */}
      <div>
        <h4 className="font-medium text-gray-900 mb-1">Description</h4>
        <p className="text-gray-600 text-sm">{job.description}</p>
      </div>

      {/* Job Details */}
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="font-medium text-gray-700">Creator:</span>
          <div className="text-gray-600">{formatAddress(job.creator)}</div>
          {isCreator && <div className="text-blue-600 text-xs">(You)</div>}
        </div>
        <div>
          <span className="font-medium text-gray-700">Assignee:</span>
          <div className="text-gray-600">{formatAddress(job.assignee)}</div>
          {isAssignee && <div className="text-blue-600 text-xs">(You)</div>}
        </div>
        <div>
          <span className="font-medium text-gray-700">Created:</span>
          <div className="text-gray-600">{formatDate(job.createdAt)}</div>
        </div>
        <div>
          <span className="font-medium text-gray-700">Completed:</span>
          <div className="text-gray-600">{formatDate(job.completedAt)}</div>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-4 border-t border-gray-200">
        {canAssign && (
          <div className="space-y-3">
            {!showAssignInput ? (
              <Button
                onClick={() => setShowAssignInput(true)}
                className="w-full"
                disabled={isLoading}
              >
                Assign Job
              </Button>
            ) : (
              <div className="space-y-2">
                <Input
                  type="text"
                  placeholder="Enter assignee address (0x...)"
                  value={assigneeAddress}
                  onChange={(e) => setAssigneeAddress(e.target.value)}
                  className="w-full"
                />
                <div className="flex space-x-2">
                  <Button
                    onClick={handleAssign}
                    disabled={isLoading || !assigneeAddress}
                    className="flex-1"
                  >
                    Confirm Assign
                  </Button>
                  <Button
                    onClick={() => {
                      setShowAssignInput(false);
                      setAssigneeAddress('');
                    }}
                    variant="outline"
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        {canStart && (
          <Button
            onClick={() => onStart?.(job.jobId)}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            Start Job
          </Button>
        )}

        {canComplete && (
          <Button
            onClick={() => onComplete?.(job.jobId)}
            disabled={isLoading}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            Complete Job & Claim Reward
          </Button>
        )}

        {canCancel && (
          <Button
            onClick={() => onCancel?.(job.jobId)}
            disabled={isLoading}
            variant="outline"
            className="w-full text-red-600 border-red-600 hover:bg-red-50"
          >
            Cancel Job
          </Button>
        )}

        {job.status === JOB_STATUS.COMPLETED && job.rewardPaid && (
          <div className="text-center py-2">
            <span className="text-green-600 font-medium">✅ Job Completed & Reward Paid</span>
          </div>
        )}
      </div>
    </Card>
  );
};
