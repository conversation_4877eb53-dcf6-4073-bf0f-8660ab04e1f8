import React, { useState } from 'react';
import { ethers } from 'ethers';
import { Card } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { CreateJobForm as CreateJobFormType } from '../types/job';

interface CreateJobFormProps {
  onCreateJob: (description: string, reward: string) => Promise<void>;
  isLoading: boolean;
  dtcBalance: bigint;
  dtcAllowance: bigint;
  minimumReward: bigint;
  onApprove: (amount: string) => Promise<void>;
}

export const CreateJobForm: React.FC<CreateJobFormProps> = ({
  onCreateJob,
  isLoading,
  dtcBalance,
  dtcAllowance,
  minimumReward,
  onApprove
}) => {
  const [form, setForm] = useState<CreateJobFormType>({
    description: '',
    reward: ''
  });
  const [needsApproval, setNeedsApproval] = useState(false);

  const handleInputChange = (field: keyof CreateJobFormType, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    
    // Check if approval is needed when reward changes
    if (field === 'reward' && value) {
      try {
        const rewardWei = ethers.parseEther(value);
        setNeedsApproval(dtcAllowance < rewardWei);
      } catch {
        setNeedsApproval(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.description.trim() || !form.reward) {
      alert('Please fill in all fields');
      return;
    }

    try {
      const rewardWei = ethers.parseEther(form.reward);
      
      // Validate minimum reward
      if (rewardWei < minimumReward) {
        alert(`Minimum reward is ${ethers.formatEther(minimumReward)} DTC`);
        return;
      }
      
      // Validate balance
      if (rewardWei > dtcBalance) {
        alert('Insufficient DTC balance');
        return;
      }

      await onCreateJob(form.description, form.reward);
      
      // Reset form on success
      setForm({ description: '', reward: '' });
      setNeedsApproval(false);
    } catch (error: any) {
      console.error('Error creating job:', error);
      alert(error.message || 'Failed to create job');
    }
  };

  const handleApprove = async () => {
    if (!form.reward) return;
    
    try {
      // Approve a bit more than needed to avoid multiple approvals
      const rewardWei = ethers.parseEther(form.reward);
      const approveAmount = rewardWei * 2n; // Approve 2x the reward amount
      await onApprove(ethers.formatEther(approveAmount));
      setNeedsApproval(false);
    } catch (error: any) {
      console.error('Error approving DTC:', error);
      alert(error.message || 'Failed to approve DTC');
    }
  };

  const isFormValid = form.description.trim() && form.reward && !isLoading;
  const rewardNumber = parseFloat(form.reward || '0');
  const minRewardNumber = parseFloat(ethers.formatEther(minimumReward));
  const balanceNumber = parseFloat(ethers.formatEther(dtcBalance));

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Create New Job</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Job Description
          </label>
          <textarea
            id="description"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Describe the job requirements and deliverables..."
            value={form.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            disabled={isLoading}
          />
        </div>

        {/* Reward */}
        <div>
          <label htmlFor="reward" className="block text-sm font-medium text-gray-700 mb-1">
            Reward (DTC)
          </label>
          <Input
            id="reward"
            type="number"
            step="0.01"
            min={ethers.formatEther(minimumReward)}
            max={ethers.formatEther(dtcBalance)}
            placeholder={`Minimum: ${ethers.formatEther(minimumReward)} DTC`}
            value={form.reward}
            onChange={(e) => handleInputChange('reward', e.target.value)}
            disabled={isLoading}
          />
          <div className="mt-1 text-xs text-gray-500">
            Your balance: {ethers.formatEther(dtcBalance)} DTC
          </div>
          {rewardNumber > 0 && rewardNumber < minRewardNumber && (
            <div className="mt-1 text-xs text-red-600">
              Minimum reward is {ethers.formatEther(minimumReward)} DTC
            </div>
          )}
          {rewardNumber > balanceNumber && (
            <div className="mt-1 text-xs text-red-600">
              Insufficient balance
            </div>
          )}
        </div>

        {/* Approval Section */}
        {needsApproval && form.reward && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-yellow-800">
                  Approval Required
                </h3>
                <div className="mt-1 text-sm text-yellow-700">
                  You need to approve DTC spending before creating this job.
                </div>
                <div className="mt-3">
                  <Button
                    type="button"
                    onClick={handleApprove}
                    disabled={isLoading}
                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  >
                    Approve {form.reward} DTC
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={!isFormValid || needsApproval}
          className="w-full"
        >
          {isLoading ? 'Creating Job...' : 'Create Job'}
        </Button>
      </form>

      {/* Info */}
      <div className="mt-4 p-3 bg-blue-50 rounded-md">
        <h4 className="text-sm font-medium text-blue-900 mb-1">How it works:</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Your DTC will be locked in the contract when you create the job</li>
          <li>• You can assign the job to any Ethereum address</li>
          <li>• The worker will receive the DTC reward when they complete the job</li>
          <li>• You can cancel unassigned or assigned jobs to get your DTC back</li>
        </ul>
      </div>
    </Card>
  );
};
