'use client';

import React from 'react';
import { useWallet } from '../contexts/WalletContext';
import { Button } from './ui/Button';

export const SimpleWalletConnect: React.FC = () => {
  const { account, isConnected, isConnecting, connectWallet, disconnectWallet, error } = useWallet();

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (isConnected && account) {
    return (
      <div className="flex items-center space-x-3">
        <div className="text-sm text-gray-600">
          {formatAddress(account)}
        </div>
        <Button
          onClick={disconnectWallet}
          variant="outline"
          size="sm"
        >
          Disconnect
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      {error && (
        <div className="text-sm text-red-600">
          {error}
        </div>
      )}
      <Button
        onClick={connectWallet}
        disabled={isConnecting}
        size="sm"
      >
        {isConnecting ? 'Connecting...' : 'Connect Wallet'}
      </Button>
    </div>
  );
};
