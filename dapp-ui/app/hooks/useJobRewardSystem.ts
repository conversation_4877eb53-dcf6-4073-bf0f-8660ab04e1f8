import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';
import { CONTRACT_ADDRESSES, JOB_REWARD_SYSTEM_ABI, DATACOIN_ABI } from '../config/contracts';
import { Job, SystemStats, UserStats } from '../types/job';

export const useJobRewardSystem = (provider: ethers.BrowserProvider | null, account: string | null) => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [dtcBalance, setDtcBalance] = useState<bigint>(0n);
  const [dtcAllowance, setDtcAllowance] = useState<bigint>(0n);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get contract instances
  const getContracts = useCallback(async () => {
    if (!provider) return null;
    
    const signer = await provider.getSigner();
    const jobRewardSystem = new ethers.Contract(
      CONTRACT_ADDRESSES.JOB_REWARD_SYSTEM,
      JOB_REWARD_SYSTEM_ABI,
      signer
    );
    const datacoin = new ethers.Contract(
      CONTRACT_ADDRESSES.DATACOIN,
      DATACOIN_ABI,
      signer
    );
    
    return { jobRewardSystem, datacoin };
  }, [provider]);

  // Load system stats
  const loadSystemStats = useCallback(async () => {
    try {
      const contracts = await getContracts();
      if (!contracts) return;

      const stats = await contracts.jobRewardSystem.getSystemStats();
      setSystemStats({
        totalJobs: stats.totalJobs,
        totalRewards: stats.totalRewards,
        minReward: stats.minReward,
        enabled: stats.enabled,
        contractBalance: stats.contractBalance
      });
    } catch (err) {
      console.error('Error loading system stats:', err);
      setError('Failed to load system stats');
    }
  }, [getContracts]);

  // Load user stats
  const loadUserStats = useCallback(async () => {
    if (!account) return;
    
    try {
      const contracts = await getContracts();
      if (!contracts) return;

      const stats = await contracts.jobRewardSystem.getUserStats(account);
      setUserStats({
        jobsCreated: stats.jobsCreated,
        jobsAssigned: stats.jobsAssigned,
        totalEarned: stats.totalEarned,
        totalSpent: stats.totalSpent
      });

      // Load DTC balance and allowance
      const balance = await contracts.datacoin.balanceOf(account);
      const allowance = await contracts.datacoin.allowance(account, CONTRACT_ADDRESSES.JOB_REWARD_SYSTEM);
      setDtcBalance(balance);
      setDtcAllowance(allowance);
    } catch (err) {
      console.error('Error loading user stats:', err);
      setError('Failed to load user stats');
    }
  }, [account, getContracts]);

  // Load available jobs
  const loadAvailableJobs = useCallback(async () => {
    try {
      const contracts = await getContracts();
      if (!contracts) return;

      const availableJobs = await contracts.jobRewardSystem.getAvailableJobs(20);
      setJobs(availableJobs);
    } catch (err) {
      console.error('Error loading jobs:', err);
      setError('Failed to load jobs');
    }
  }, [getContracts]);

  // Create job
  const createJob = useCallback(async (description: string, reward: string) => {
    if (!provider || !account) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const rewardWei = ethers.parseEther(reward);
      
      // Check if user has enough DTC
      if (dtcBalance < rewardWei) {
        throw new Error('Insufficient DTC balance');
      }
      
      // Check if allowance is sufficient
      if (dtcAllowance < rewardWei) {
        // Approve first
        const approveTx = await contracts.datacoin.approve(
          CONTRACT_ADDRESSES.JOB_REWARD_SYSTEM,
          rewardWei
        );
        await approveTx.wait();
      }
      
      // Create job
      const tx = await contracts.jobRewardSystem.createJob(description, rewardWei);
      await tx.wait();
      
      // Reload data
      await Promise.all([loadSystemStats(), loadUserStats(), loadAvailableJobs()]);
      
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to create job');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, account, dtcBalance, dtcAllowance, getContracts, loadSystemStats, loadUserStats, loadAvailableJobs]);

  // Assign job
  const assignJob = useCallback(async (jobId: bigint, assignee: string) => {
    if (!provider) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const tx = await contracts.jobRewardSystem.assignJob(jobId, assignee);
      await tx.wait();
      
      await loadAvailableJobs();
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to assign job');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, getContracts, loadAvailableJobs]);

  // Start job
  const startJob = useCallback(async (jobId: bigint) => {
    if (!provider) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const tx = await contracts.jobRewardSystem.startJob(jobId);
      await tx.wait();
      
      await loadAvailableJobs();
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to start job');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, getContracts, loadAvailableJobs]);

  // Complete job
  const completeJob = useCallback(async (jobId: bigint) => {
    if (!provider) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const tx = await contracts.jobRewardSystem.completeJob(jobId);
      await tx.wait();
      
      // Reload data
      await Promise.all([loadSystemStats(), loadUserStats(), loadAvailableJobs()]);
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to complete job');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, getContracts, loadSystemStats, loadUserStats, loadAvailableJobs]);

  // Cancel job
  const cancelJob = useCallback(async (jobId: bigint) => {
    if (!provider) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const tx = await contracts.jobRewardSystem.cancelJob(jobId);
      await tx.wait();
      
      await Promise.all([loadSystemStats(), loadUserStats(), loadAvailableJobs()]);
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to cancel job');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, getContracts, loadSystemStats, loadUserStats, loadAvailableJobs]);

  // Approve DTC spending
  const approveDTC = useCallback(async (amount: string) => {
    if (!provider) throw new Error('Wallet not connected');
    
    setIsLoading(true);
    setError(null);
    
    try {
      const contracts = await getContracts();
      if (!contracts) throw new Error('Failed to get contracts');

      const amountWei = ethers.parseEther(amount);
      const tx = await contracts.datacoin.approve(CONTRACT_ADDRESSES.JOB_REWARD_SYSTEM, amountWei);
      await tx.wait();
      
      await loadUserStats();
      return tx.hash;
    } catch (err: any) {
      setError(err.message || 'Failed to approve DTC');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [provider, getContracts, loadUserStats]);

  // Load all data
  const loadData = useCallback(async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadSystemStats(),
        loadUserStats(),
        loadAvailableJobs()
      ]);
    } catch (err) {
      console.error('Error loading data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [loadSystemStats, loadUserStats, loadAvailableJobs]);

  // Load data when provider or account changes
  useEffect(() => {
    if (provider && account) {
      loadData();
    }
  }, [provider, account, loadData]);

  return {
    // State
    jobs,
    systemStats,
    userStats,
    dtcBalance,
    dtcAllowance,
    isLoading,
    error,
    
    // Actions
    createJob,
    assignJob,
    startJob,
    completeJob,
    cancelJob,
    approveDTC,
    loadData,
    
    // Utils
    clearError: () => setError(null)
  };
};
