'use client';

import React from 'react';
import { useWallet } from '../contexts/WalletContext';
import { JobDashboard } from '../components/JobDashboard';
import { SimpleWalletConnect } from '../components/SimpleWalletConnect';

export default function JobsPage() {
  const { provider, account, isConnecting } = useWallet();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                DATACOIN Job Marketplace
              </h1>
            </div>
            <SimpleWalletConnect />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <JobDashboard provider={provider} account={account} />
      </main>
    </div>
  );
}
