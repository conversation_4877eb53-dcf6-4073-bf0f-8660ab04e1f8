// Contract addresses and ABIs for Job Reward System

export const CONTRACT_ADDRESSES = {
  // Updated with deployed contract addresses
  DATACOIN: "0x5FbDB2315678afecb367f032d93F642f64180aa3",
  JOB_REWARD_SYSTEM: "0xCf7Ed3AccA5a467e9e704C703E8D87F634fB0Fc9"
};

export const DATACOIN_ABI = [
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
  "function totalSupply() view returns (uint256)",
  "function balanceOf(address) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function transferFrom(address from, address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",
  "function mint(address to, uint256 amount)",
  "function owner() view returns (address)",
  "event Transfer(address indexed from, address indexed to, uint256 value)",
  "event Approval(address indexed owner, address indexed spender, uint256 value)"
];

export const JOB_REWARD_SYSTEM_ABI = [
  // Job management functions
  "function createJob(string description, uint256 reward) returns (uint256)",
  "function assignJob(uint256 jobId, address assignee)",
  "function startJob(uint256 jobId)",
  "function completeJob(uint256 jobId)",
  "function cancelJob(uint256 jobId)",

  // View functions
  "function getJob(uint256 jobId) view returns (tuple(uint256 jobId, address creator, address assignee, string description, uint256 reward, uint8 status, uint256 createdAt, uint256 completedAt, bool rewardPaid))",
  "function getUserCreatedJobs(address user) view returns (uint256[])",
  "function getUserAssignedJobs(address user) view returns (uint256[])",
  "function getAvailableJobs(uint256 limit) view returns (tuple(uint256 jobId, address creator, address assignee, string description, uint256 reward, uint8 status, uint256 createdAt, uint256 completedAt, bool rewardPaid)[])",
  "function getSystemStats() view returns (uint256 totalJobs, uint256 totalRewards, uint256 minReward, bool enabled, uint256 contractBalance)",
  "function getUserStats(address user) view returns (uint256 jobsCreated, uint256 jobsAssigned, uint256 totalEarned, uint256 totalSpent)",

  // State variables
  "function jobCounter() view returns (uint256)",
  "function totalRewardsPaid() view returns (uint256)",
  "function minimumReward() view returns (uint256)",
  "function systemEnabled() view returns (bool)",
  "function datacoinToken() view returns (address)",
  "function owner() view returns (address)",

  // Owner functions
  "function toggleSystem()",
  "function setMinimumReward(uint256 minimumReward)",

  // Events
  "event JobCreated(uint256 indexed jobId, address indexed creator, string description, uint256 reward)",
  "event JobAssigned(uint256 indexed jobId, address indexed assignee)",
  "event JobStarted(uint256 indexed jobId, address indexed assignee)",
  "event JobCompleted(uint256 indexed jobId, address indexed assignee, uint256 reward)",
  "event JobCancelled(uint256 indexed jobId, address indexed creator)",
  "event RewardPaid(uint256 indexed jobId, address indexed recipient, uint256 amount)"
];

// Job status enum
export const JOB_STATUS = {
  CREATED: 0,
  ASSIGNED: 1,
  IN_PROGRESS: 2,
  COMPLETED: 3,
  CANCELLED: 4
} as const;

export const JOB_STATUS_LABELS = {
  [JOB_STATUS.CREATED]: "Available",
  [JOB_STATUS.ASSIGNED]: "Assigned",
  [JOB_STATUS.IN_PROGRESS]: "In Progress",
  [JOB_STATUS.COMPLETED]: "Completed",
  [JOB_STATUS.CANCELLED]: "Cancelled"
} as const;

export const JOB_STATUS_COLORS = {
  [JOB_STATUS.CREATED]: "bg-green-100 text-green-800",
  [JOB_STATUS.ASSIGNED]: "bg-blue-100 text-blue-800",
  [JOB_STATUS.IN_PROGRESS]: "bg-yellow-100 text-yellow-800",
  [JOB_STATUS.COMPLETED]: "bg-gray-100 text-gray-800",
  [JOB_STATUS.CANCELLED]: "bg-red-100 text-red-800"
} as const;
