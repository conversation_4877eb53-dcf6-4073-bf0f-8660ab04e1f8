export interface Job {
  jobId: bigint;
  creator: string;
  assignee: string;
  description: string;
  reward: bigint;
  status: number;
  createdAt: bigint;
  completedAt: bigint;
  rewardPaid: boolean;
}

export interface SystemStats {
  totalJobs: bigint;
  totalRewards: bigint;
  minReward: bigint;
  enabled: boolean;
  contractBalance: bigint;
}

export interface UserStats {
  jobsCreated: bigint;
  jobsAssigned: bigint;
  totalEarned: bigint;
  totalSpent: bigint;
}

export interface CreateJobForm {
  description: string;
  reward: string;
}

export interface JobCardProps {
  job: Job;
  currentUser: string;
  onAssign?: (jobId: bigint, assignee: string) => void;
  onStart?: (jobId: bigint) => void;
  onComplete?: (jobId: bigint) => void;
  onCancel?: (jobId: bigint) => void;
  isLoading?: boolean;
}
