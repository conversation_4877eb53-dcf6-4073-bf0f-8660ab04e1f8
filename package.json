{"name": "datacoin-token", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "compile": "npx hardhat compile", "deploy": "npx hardhat run scripts/deploy-dtcerc.js", "deploy:local": "npx hardhat run scripts/deploy-dtcerc.js --network localhost", "deploy:ethermint": "npx hardhat run scripts/deploy-dtcerc.js --network ethermint", "mint": "npx hardhat run scripts/mint-tokens.js", "mint:local": "npx hardhat run scripts/mint-tokens.js --network localhost", "mint:ethermint": "npx hardhat run scripts/mint-tokens.js --network ethermint", "verify": "npx hardhat run scripts/verify-contract.js", "verify:local": "npx hardhat run scripts/verify-contract.js --network localhost", "verify:ethermint": "npx hardhat run scripts/verify-contract.js --network ethermint", "balance": "npx hardhat run scripts/check-balance.js", "balance:ethermint": "npx hardhat run scripts/check-balance.js --network ethermint", "node": "npx hardhat node"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "dotenv": "^17.2.0", "hardhat": "^2.26.1"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0"}}