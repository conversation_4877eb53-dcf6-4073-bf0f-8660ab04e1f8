async function main() {
  console.log("🚀 Demo Hardhat Ignition Modules for DATACOIN");
  console.log("=".repeat(60));
  
  // All deployed contracts from Ignition
  const deployedContracts = [
    {
      module: "DatacoinDeployModule",
      address: "******************************************",
      description: "Basic deployment without minting"
    },
    {
      module: "DatacoinWithMintModule", 
      address: "******************************************",
      description: "Deployment with automatic minting"
    },
    {
      module: "DatacoinSimpleModule",
      address: "******************************************", 
      description: "Simple deployment with parameters"
    }
  ];
  
  const [deployer] = await ethers.getSigners();
  console.log("📋 Network Info:");
  console.log("  - Network:", hre.network.name);
  console.log("  - Chain ID:", (await ethers.provider.getNetwork()).chainId);
  console.log("  - Account:", deployer.address);
  console.log("  - ETH Balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));
  
  console.log("\n📊 Deployed Contracts Summary:");
  console.log("-".repeat(60));
  
  for (const contract of deployedContracts) {
    console.log(`\n🏗️  ${contract.module}`);
    console.log(`   Address: ${contract.address}`);
    console.log(`   Description: ${contract.description}`);
    
    try {
      const DATACOIN = await ethers.getContractFactory("DATACOIN");
      const token = DATACOIN.attach(contract.address);
      
      const totalSupply = await token.totalSupply();
      const deployerBalance = await token.balanceOf(deployer.address);
      
      console.log(`   Total Supply: ${ethers.formatEther(totalSupply)} DTC`);
      console.log(`   Deployer Balance: ${ethers.formatEther(deployerBalance)} DTC`);
      
      // Check specific addresses for different modules
      if (contract.module === "DatacoinWithMintModule") {
        const testAddress = "******************************************";
        const testBalance = await token.balanceOf(testAddress);
        console.log(`   Test Address Balance: ${ethers.formatEther(testBalance)} DTC`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error reading contract: ${error.message}`);
    }
  }
  
  console.log("\n🧪 Testing Interactions:");
  console.log("-".repeat(60));
  
  // Test with the contract that has the most tokens
  const testContract = deployedContracts.find(c => c.module === "DatacoinSimpleModule");
  if (testContract) {
    console.log(`\n🎯 Testing with ${testContract.module}:`);
    
    try {
      const DATACOIN = await ethers.getContractFactory("DATACOIN");
      const token = DATACOIN.attach(testContract.address);
      
      const balance = await token.balanceOf(deployer.address);
      console.log(`   Current Balance: ${ethers.formatEther(balance)} DTC`);
      
      if (balance > ethers.parseEther("100")) {
        // Test transfer
        const recipient = "0x8ba1f109551bd432803012645hac136c0c8326b1";
        const transferAmount = ethers.parseEther("50");
        
        console.log(`   🔄 Transferring 50 DTC to ${recipient}...`);
        const tx = await token.transfer(recipient, transferAmount, {
          gasLimit: 100000
        });
        
        console.log(`   Transaction Hash: ${tx.hash}`);
        await tx.wait();
        
        const newBalance = await token.balanceOf(recipient);
        console.log(`   Recipient Balance: ${ethers.formatEther(newBalance)} DTC`);
        console.log(`   ✅ Transfer successful!`);
        
        // Test approve
        const approveAmount = ethers.parseEther("25");
        console.log(`   🔐 Approving 25 DTC for ${recipient}...`);
        
        const approveTx = await token.approve(recipient, approveAmount, {
          gasLimit: 100000
        });
        
        await approveTx.wait();
        const allowance = await token.allowance(deployer.address, recipient);
        console.log(`   Allowance: ${ethers.formatEther(allowance)} DTC`);
        console.log(`   ✅ Approve successful!`);
      }
      
    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
    }
  }
  
  console.log("\n📚 Module Usage Examples:");
  console.log("-".repeat(60));
  console.log(`
1. Basic Deployment:
   npx hardhat ignition deploy ignition/modules/DatacoinDeploy.js --network ethermint

2. Deployment with Minting:
   npx hardhat ignition deploy ignition/modules/DatacoinWithMint.js --network ethermint

3. Simple Deployment with Parameters:
   npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint

4. Custom Parameters via CLI:
   npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint \\
     --parameters '{"owner": "******************************************", "mintAmount": "1000000000000000000000000"}'

5. Using Parameters File:
   Edit ignition/parameters/ethermint.json and run:
   npx hardhat ignition deploy ignition/modules/DatacoinSimple.js --network ethermint
  `);
  
  console.log("\n🎉 Demo completed successfully!");
  console.log("All Ignition modules are working properly with Ethermint!");
}

main().catch((error) => {
  console.error("💥 Demo failed:", error);
  process.exitCode = 1;
});
