const { ethers } = require("hardhat");

async function checkContract(contractAddress) {
    console.log(`Checking contract at address: ${contractAddress}`);
    
    try {
        // Check if there's code at the address
        const code = await ethers.provider.getCode(contractAddress);
        
        if (code === "0x") {
            console.log("❌ No contract found at this address");
            return false;
        }
        
        console.log("✅ Contract exists at this address");
        console.log(`Contract bytecode length: ${code.length} characters`);
        
        // Try to interact with the contract
        const JobExecutor = await ethers.getContractFactory("JobExecutor");
        const jobExecutor = JobExecutor.attach(contractAddress);
        
        try {
            const owner = await jobExecutor.owner();
            const jobCounter = await jobExecutor.jobCounter();
            const totalJobs = await jobExecutor.totalJobsExecuted();
            const enabled = await jobExecutor.jobExecutionEnabled();
            
            console.log("\n📊 Contract Information:");
            console.log(`Owner: ${owner}`);
            console.log(`Job Counter: ${jobCounter}`);
            console.log(`Total Jobs Executed: ${totalJobs}`);
            console.log(`Job Execution Enabled: ${enabled}`);
            
            return true;
        } catch (error) {
            console.log("⚠️  Contract exists but might not be JobExecutor or has different ABI");
            console.log("Error:", error.message);
            return false;
        }
        
    } catch (error) {
        console.log("❌ Error checking contract:", error.message);
        return false;
    }
}

async function main() {
    const contractAddresses = [
        "0x5FbDB2315678afecb367f032d93F642f64180aa3", // Your current contract
        // Add more addresses here if needed
    ];
    
    console.log("🔍 Checking JobExecutor contracts...\n");
    
    for (const address of contractAddresses) {
        await checkContract(address);
        console.log("─".repeat(50));
    }
}

main()
    .then(() => {
        console.log("\nContract check completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("Check failed:", error);
        process.exit(1);
    });
