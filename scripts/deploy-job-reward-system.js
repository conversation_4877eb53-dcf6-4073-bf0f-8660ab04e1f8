const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 Deploying Job Reward System with DTC Token Integration");
    console.log("═".repeat(70));

    // Get signers
    const [deployer, creator, worker1, worker2] = await ethers.getSigners();
    console.log("Deployer:", deployer.address);
    console.log("Creator:", creator.address);
    console.log("Worker1:", worker1.address);
    console.log("Worker2:", worker2.address);

    // Deploy DATACOIN token first
    console.log("\n📦 Deploying DATACOIN token...");
    const DATACOIN = await ethers.getContractFactory("DATACOIN");
    const datacoin = await DATACOIN.deploy(deployer.address);
    await datacoin.waitForDeployment();
    
    const datacoinAddress = await datacoin.getAddress();
    console.log("✅ DATACOIN deployed at:", datacoinAddress);

    // Mint initial tokens
    const initialSupply = ethers.parseEther("10000"); // 10,000 DTC
    console.log("\n💰 Minting initial tokens...");
    
    await datacoin.mint(deployer.address, initialSupply);
    await datacoin.mint(creator.address, initialSupply);
    console.log("✅ Minted", ethers.formatEther(initialSupply), "DTC to deployer");
    console.log("✅ Minted", ethers.formatEther(initialSupply), "DTC to creator");

    // Deploy JobRewardSystem
    console.log("\n📦 Deploying JobRewardSystem...");
    const minimumReward = ethers.parseEther("10"); // 10 DTC minimum
    
    const JobRewardSystem = await ethers.getContractFactory("JobRewardSystem");
    const jobRewardSystem = await JobRewardSystem.deploy(
        deployer.address,
        datacoinAddress,
        minimumReward
    );
    await jobRewardSystem.waitForDeployment();
    
    const jobRewardSystemAddress = await jobRewardSystem.getAddress();
    console.log("✅ JobRewardSystem deployed at:", jobRewardSystemAddress);
    console.log("✅ Minimum reward:", ethers.formatEther(minimumReward), "DTC");

    // Approve JobRewardSystem to spend tokens
    console.log("\n🔐 Setting up token approvals...");
    const approvalAmount = ethers.parseEther("1000"); // 1,000 DTC
    
    await datacoin.connect(creator).approve(jobRewardSystemAddress, approvalAmount);
    console.log("✅ Creator approved", ethers.formatEther(approvalAmount), "DTC for JobRewardSystem");

    // Test the system with a complete job workflow
    console.log("\n🧪 Testing complete job workflow...");
    console.log("─".repeat(50));

    try {
        // Step 1: Create job
        const jobDescription = "Test job: Process blockchain data";
        const jobReward = ethers.parseEther("50"); // 50 DTC
        
        console.log("\n1️⃣ Creating job...");
        console.log("   Description:", jobDescription);
        console.log("   Reward:", ethers.formatEther(jobReward), "DTC");
        
        const creatorBalanceBefore = await datacoin.balanceOf(creator.address);
        console.log("   Creator balance before:", ethers.formatEther(creatorBalanceBefore), "DTC");
        
        const tx1 = await jobRewardSystem.connect(creator).createJob(jobDescription, jobReward);
        await tx1.wait();
        
        const creatorBalanceAfter = await datacoin.balanceOf(creator.address);
        console.log("   Creator balance after:", ethers.formatEther(creatorBalanceAfter), "DTC");
        console.log("   ✅ Job created successfully! Job ID: 1");

        // Step 2: Assign job
        console.log("\n2️⃣ Assigning job to worker1...");
        const tx2 = await jobRewardSystem.connect(creator).assignJob(1, worker1.address);
        await tx2.wait();
        console.log("   ✅ Job assigned to worker1");

        // Step 3: Start job
        console.log("\n3️⃣ Worker1 starting job...");
        const tx3 = await jobRewardSystem.connect(worker1).startJob(1);
        await tx3.wait();
        console.log("   ✅ Job started");

        // Step 4: Complete job and receive reward
        console.log("\n4️⃣ Worker1 completing job...");
        const worker1BalanceBefore = await datacoin.balanceOf(worker1.address);
        console.log("   Worker1 balance before:", ethers.formatEther(worker1BalanceBefore), "DTC");
        
        const tx4 = await jobRewardSystem.connect(worker1).completeJob(1);
        await tx4.wait();
        
        const worker1BalanceAfter = await datacoin.balanceOf(worker1.address);
        const earned = worker1BalanceAfter - worker1BalanceBefore;
        
        console.log("   Worker1 balance after:", ethers.formatEther(worker1BalanceAfter), "DTC");
        console.log("   💰 Worker1 earned:", ethers.formatEther(earned), "DTC");
        console.log("   ✅ Job completed and reward paid!");

        // Verify job details
        console.log("\n📋 Job completion verification...");
        const job = await jobRewardSystem.getJob(1);
        console.log("   Job status:", job.status === 3n ? "COMPLETED" : "OTHER");
        console.log("   Reward paid:", job.rewardPaid);
        console.log("   Assignee:", job.assignee);
        console.log("   Reward amount:", ethers.formatEther(job.reward), "DTC");

        // Check system stats
        console.log("\n📊 System statistics...");
        const stats = await jobRewardSystem.getSystemStats();
        console.log("   Total jobs:", stats.totalJobs.toString());
        console.log("   Total rewards paid:", ethers.formatEther(stats.totalRewards), "DTC");
        console.log("   Contract DTC balance:", ethers.formatEther(stats.contractBalance), "DTC");

        // Check user stats
        const workerStats = await jobRewardSystem.getUserStats(worker1.address);
        const creatorStats = await jobRewardSystem.getUserStats(creator.address);
        
        console.log("\n👤 User statistics...");
        console.log("   Worker1 total earned:", ethers.formatEther(workerStats.totalEarned), "DTC");
        console.log("   Worker1 jobs assigned:", workerStats.jobsAssigned.toString());
        console.log("   Creator total spent:", ethers.formatEther(creatorStats.totalSpent), "DTC");
        console.log("   Creator jobs created:", creatorStats.jobsCreated.toString());

        // Test creating another job
        console.log("\n🔄 Creating second job...");
        const job2Description = "Test job 2: Generate reports";
        const job2Reward = ethers.parseEther("75"); // 75 DTC
        
        const tx5 = await jobRewardSystem.connect(creator).createJob(job2Description, job2Reward);
        await tx5.wait();
        console.log("   ✅ Second job created! Job ID: 2");
        console.log("   Description:", job2Description);
        console.log("   Reward:", ethers.formatEther(job2Reward), "DTC");

        // Check available jobs
        const availableJobs = await jobRewardSystem.getAvailableJobs(10);
        console.log("\n📋 Available jobs:", availableJobs.length);
        availableJobs.forEach((job, index) => {
            console.log(`   ${index + 1}. Job ${job.jobId}: "${job.description}" - ${ethers.formatEther(job.reward)} DTC`);
        });

        console.log("\n🎉 Deployment and testing completed successfully!");
        console.log("═".repeat(70));
        console.log("📝 Contract Addresses:");
        console.log("   DATACOIN Token:", datacoinAddress);
        console.log("   JobRewardSystem:", jobRewardSystemAddress);
        console.log("═".repeat(70));

        return {
            datacoin: datacoinAddress,
            jobRewardSystem: jobRewardSystemAddress
        };

    } catch (error) {
        console.error("❌ Testing failed:", error.message);
        throw error;
    }
}

main()
    .then((addresses) => {
        console.log("\n🏆 Deployment successful!");
        console.log("DATACOIN:", addresses.datacoin);
        console.log("JobRewardSystem:", addresses.jobRewardSystem);
        process.exit(0);
    })
    .catch((error) => {
        console.error("💥 Deployment failed:", error);
        process.exit(1);
    });
