async function main() {
  console.log("🪙 Starting DTCERC Token Minting");
  console.log("=" .repeat(40));

  const [deployer] = await ethers.getSigners();

  // Contract address - update this with your deployed contract address
  const CONTRACT_ADDRESS = "******************************************"; // Update this!

  console.log("📋 Minting Details:");
  console.log("  Minter address:", deployer.address);
  console.log("  Contract address:", CONTRACT_ADDRESS);

  // Connect to the deployed contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = await DATACOIN.attach(CONTRACT_ADDRESS);

  // Verify contract connection
  console.log("  Token name:", await token.name());
  console.log("  Token symbol:", await token.symbol());
  console.log("  Current owner:", await token.owner());

  // Check current supply
  const currentSupply = await token.totalSupply();
  console.log("  Current total supply:", ethers.formatEther(currentSupply), "DTCERC");

  // Mint tokens to deployer (for testing)
  const mintAmount = ethers.parseEther("1000000"); // 1 million tokens

  console.log("\n🔨 Minting tokens...");
  console.log("  Amount to mint:", ethers.formatEther(mintAmount), "DTCERC");
  console.log("  Recipient:", deployer.address);

  const mintTx = await token.mint(deployer.address, mintAmount);
  console.log("  Transaction hash:", mintTx.hash);

  console.log("⏳ Waiting for confirmation...");
  await mintTx.wait();

  // Check new balances
  const newSupply = await token.totalSupply();
  const deployerBalance = await token.balanceOf(deployer.address);

  console.log("\n✅ Minting Successful!");
  console.log("=" .repeat(40));
  console.log("📊 Updated Information:");
  console.log("  Total supply:", ethers.formatEther(newSupply), "DTCERC");
  console.log("  Your balance:", ethers.formatEther(deployerBalance), "DTCERC");
  console.log("  Tokens minted:", ethers.formatEther(mintAmount), "DTCERC");

  console.log("\n📝 Next Steps:");
  console.log("1. Add DTCERC token to MetaMask:");
  console.log(`   - Contract Address: ${CONTRACT_ADDRESS}`);
  console.log(`   - Token Symbol: DTCERC`);
  console.log(`   - Decimals: 18`);
  console.log("2. Your wallet should now show", ethers.formatEther(deployerBalance), "DTCERC tokens");
  console.log("3. You can transfer tokens using the dApp frontend");

  console.log("\n🎉 Token minting completed successfully!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("\n❌ Minting failed:");
    console.error(error);
    process.exitCode = 1;
  });
