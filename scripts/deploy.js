async function main() {
  console.log("DATACOIN ERC (DTCERC) deployment start");
  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));

  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = await DATACOIN.deploy(deployer.address, {
    gasLimit: 2000000,
    gasPrice: ethers.parseUnits("20", "gwei")
  });

  await token.waitForDeployment();

  const contractAddress = await token.getAddress();
  console.log("DATACOIN ERC (DTCERC) deployed to:", contractAddress);
  console.log("Transaction hash:", token.deploymentTransaction().hash);

  // Verify token details
  console.log("\n=== Token Details ===");
  console.log("Name:", await token.name());
  console.log("Symbol:", await token.symbol());
  console.log("Decimals:", await token.decimals());
  console.log("Owner:", await token.owner());
  console.log("Total Supply:", ethers.formatEther(await token.totalSupply()));
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
