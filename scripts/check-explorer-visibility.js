async function main() {
  console.log("🔍 Checking DTC Token Visibility on Explorer");
  console.log("=".repeat(60));
  
  const contractAddress = "******************************************";
  const [deployer] = await ethers.getSigners();
  
  console.log("📋 Basic Info:");
  console.log("- Network:", hre.network.name);
  console.log("- Chain ID:", (await ethers.provider.getNetwork()).chainId);
  console.log("- RPC URL:", "http://127.0.0.1:8545");
  console.log("- Contract Address:", contractAddress);
  console.log("- Deployer Address:", deployer.address);
  
  // Kết nối với contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  console.log("\n📄 Contract Information:");
  try {
    const name = await token.name();
    const symbol = await token.symbol();
    const decimals = await token.decimals();
    const totalSupply = await token.totalSupply();
    
    console.log("- Name:", name);
    console.log("- Symbol:", symbol);
    console.log("- Decimals:", decimals.toString());
    console.log("- Total Supply:", ethers.formatEther(totalSupply));
    console.log("✅ Contract is accessible");
    
  } catch (error) {
    console.log("❌ Cannot read contract:", error.message);
    return;
  }
  
  console.log("\n🔗 Explorer URLs to Check:");
  console.log("1. Contract Page:");
  console.log(`   http://localhost:8545/address/${contractAddress}`);
  console.log("2. Deployer Address:");
  console.log(`   http://localhost:8545/address/${deployer.address}`);
  
  // Kiểm tra recent transactions
  console.log("\n📊 Recent Transaction Analysis:");
  const blockNumber = await ethers.provider.getBlockNumber();
  console.log("- Current Block:", blockNumber);
  
  // Lấy một vài block gần đây để tìm transactions
  const recentBlocks = [];
  for (let i = Math.max(1, blockNumber - 10); i <= blockNumber; i++) {
    try {
      const block = await ethers.provider.getBlock(i, true);
      if (block && block.transactions.length > 0) {
        recentBlocks.push({
          number: i,
          hash: block.hash,
          txCount: block.transactions.length,
          transactions: block.transactions.slice(0, 3) // Chỉ lấy 3 tx đầu
        });
      }
    } catch (error) {
      console.log(`  ⚠️ Cannot get block ${i}`);
    }
  }
  
  console.log(`\n📦 Recent Blocks with Transactions:`);
  for (const block of recentBlocks) {
    console.log(`  Block ${block.number}: ${block.txCount} transactions`);
    for (const tx of block.transactions) {
      if (typeof tx === 'object' && tx.hash) {
        console.log(`    - TX: ${tx.hash}`);
        console.log(`      From: ${tx.from}`);
        console.log(`      To: ${tx.to}`);
        console.log(`      Value: ${ethers.formatEther(tx.value || 0)} ETH`);
        
        // Kiểm tra nếu là transaction với contract DTC
        if (tx.to && tx.to.toLowerCase() === contractAddress.toLowerCase()) {
          console.log(`      🎯 This is a DTC contract transaction!`);
        }
      }
    }
  }
  
  // Tạo một transaction mới để test
  console.log("\n🧪 Creating Test Transaction for Explorer:");
  try {
    const testRecipient = "******************************************";
    const testAmount = ethers.parseEther("1");
    
    console.log(`- Sending 1 DTC to ${testRecipient}...`);
    
    const tx = await token.transfer(testRecipient, testAmount, {
      gasLimit: 100000
    });
    
    console.log(`- Transaction Hash: ${tx.hash}`);
    console.log(`- Waiting for confirmation...`);
    
    const receipt = await tx.wait();
    
    console.log(`- ✅ Confirmed in Block: ${receipt.blockNumber}`);
    console.log(`- Gas Used: ${receipt.gasUsed.toString()}`);
    
    // URLs để check trên explorer
    console.log(`\n🔍 Check these URLs on your explorer:`);
    console.log(`1. Transaction: http://localhost:8545/tx/${tx.hash}`);
    console.log(`2. Block: http://localhost:8545/block/${receipt.blockNumber}`);
    console.log(`3. Contract: http://localhost:8545/address/${contractAddress}`);
    console.log(`4. Recipient: http://localhost:8545/address/${testRecipient}`);
    
  } catch (error) {
    console.log(`❌ Test transaction failed: ${error.message}`);
  }
  
  // Kiểm tra balances
  console.log("\n💰 Token Balances:");
  const testAddresses = [
    deployer.address,
    "******************************************",
    "******************************************",
    "******************************************"
  ];
  
  for (const addr of testAddresses) {
    try {
      const balance = await token.balanceOf(addr);
      const ethBalance = await ethers.provider.getBalance(addr);
      
      console.log(`${addr}:`);
      console.log(`  - DTC: ${ethers.formatEther(balance)}`);
      console.log(`  - ETH: ${ethers.formatEther(ethBalance)}`);
      
    } catch (error) {
      console.log(`${addr}: ❌ Error reading balance`);
    }
  }
  
  console.log("\n💡 Troubleshooting Tips:");
  console.log("1. Make sure your Ethermint explorer is running on http://localhost:8545");
  console.log("2. Check if the explorer supports ERC-20 token detection");
  console.log("3. Try manually adding the token contract address to the explorer");
  console.log("4. Verify the explorer is connected to the same network (Chain ID: 9000)");
  console.log("5. Some explorers need time to index new contracts");
  
  console.log("\n📝 Token Contract Details for Manual Addition:");
  console.log(`- Contract Address: ${contractAddress}`);
  console.log(`- Token Name: DATACOIN`);
  console.log(`- Token Symbol: DTC`);
  console.log(`- Decimals: 18`);
  
  console.log("\n🎉 Check completed!");
}

main().catch((error) => {
  console.error("💥 Check failed:", error);
  process.exitCode = 1;
});
