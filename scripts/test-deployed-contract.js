const { ethers } = require("hardhat");

// Địa chỉ contract đã deploy
const CONTRACT_ADDRESS = "******************************************";

async function main() {
    console.log("🧪 Testing deployed JobExecutor contract...");
    console.log("Contract Address:", CONTRACT_ADDRESS);
    console.log("─".repeat(60));

    // Get contract instance
    const JobExecutor = await ethers.getContractFactory("JobExecutor");
    const jobExecutor = JobExecutor.attach(CONTRACT_ADDRESS);

    // Get signers
    const [owner, user1, user2] = await ethers.getSigners();
    console.log("Owner:", owner.address);
    console.log("User1:", user1.address);
    console.log("User2:", user2.address);

    try {
        // Test 1: Check contract state
        console.log("\n📊 Test 1: Contract State");
        const stats = await jobExecutor.getContractStats();
        console.log("✅ Total jobs executed:", stats.totalJobs.toString());
        console.log("✅ Current job ID:", stats.currentJobId.toString());
        console.log("✅ Execution enabled:", stats.executionEnabled);
        console.log("✅ Contract owner:", await jobExecutor.owner());

        // Test 2: Execute simple job
        console.log("\n🚀 Test 2: Execute Simple Job");
        const jobData = `Test job executed at ${new Date().toISOString()}`;
        const tx1 = await jobExecutor.connect(user1).executeJob(jobData);
        await tx1.wait();
        console.log("✅ Simple job executed successfully");
        console.log("   Job data:", jobData);

        // Test 3: Execute calculation job
        console.log("\n🧮 Test 3: Execute Calculation Job");
        const input = 8;
        const tx2 = await jobExecutor.connect(user2).executeCalculationJob(input);
        const receipt2 = await tx2.wait();
        console.log("✅ Calculation job executed successfully");
        console.log("   Input:", input);

        // Test 4: Execute batch jobs
        console.log("\n📦 Test 4: Execute Batch Jobs");
        const batchJobs = [
            "Batch job 1: Data processing",
            "Batch job 2: File validation",
            "Batch job 3: Report generation"
        ];
        const tx3 = await jobExecutor.connect(user1).executeBatchJobs(batchJobs);
        await tx3.wait();
        console.log("✅ Batch jobs executed successfully");
        console.log("   Number of jobs:", batchJobs.length);

        // Test 5: Check updated stats
        console.log("\n📈 Test 5: Updated Contract Stats");
        const newStats = await jobExecutor.getContractStats();
        console.log("✅ New total jobs:", newStats.totalJobs.toString());
        console.log("✅ New job ID:", newStats.currentJobId.toString());

        // Test 6: Get job execution details
        console.log("\n🔍 Test 6: Job Execution Details");
        const latestJobId = newStats.currentJobId;
        if (latestJobId > 0) {
            const jobExecution = await jobExecutor.getJobExecution(latestJobId);
            console.log("✅ Latest job details:");
            console.log("   Job ID:", jobExecution.jobId.toString());
            console.log("   Executor:", jobExecution.executor);
            console.log("   Job Data:", jobExecution.jobData);
            console.log("   Timestamp:", new Date(Number(jobExecution.timestamp) * 1000).toISOString());
        }

        // Test 7: Get user job history
        console.log("\n📚 Test 7: User Job History");
        const user1Jobs = await jobExecutor.getUserJobHistory(user1.address, 10);
        const user2Jobs = await jobExecutor.getUserJobHistory(user2.address, 10);
        console.log("✅ User1 job count:", user1Jobs.length);
        console.log("✅ User2 job count:", user2Jobs.length);

        // Test 8: Get latest jobs
        console.log("\n🕐 Test 8: Latest Jobs");
        const latestJobs = await jobExecutor.getLatestJobs(3);
        console.log("✅ Latest 3 jobs:");
        latestJobs.forEach((job, index) => {
            console.log(`   ${index + 1}. Job ${job.jobId}: "${job.jobData.substring(0, 50)}..."`);
        });

        // Test 9: Check if specific jobs exist
        console.log("\n✔️  Test 9: Job Existence Check");
        const jobExists1 = await jobExecutor.isJobExecuted(1);
        const jobExists999 = await jobExecutor.isJobExecuted(999);
        console.log("✅ Job ID 1 exists:", jobExists1);
        console.log("✅ Job ID 999 exists:", jobExists999);

        // Test 10: Owner functions (if you're the owner)
        console.log("\n👑 Test 10: Owner Functions");
        try {
            const currentOwner = await jobExecutor.owner();
            if (currentOwner.toLowerCase() === owner.address.toLowerCase()) {
                console.log("✅ Testing owner functions...");

                // Test toggle (disable then enable)
                await jobExecutor.connect(owner).toggleJobExecution();
                const disabledState = await jobExecutor.jobExecutionEnabled();
                console.log("   Job execution disabled:", !disabledState);

                await jobExecutor.connect(owner).toggleJobExecution();
                const enabledState = await jobExecutor.jobExecutionEnabled();
                console.log("   Job execution re-enabled:", enabledState);

                // Test update job data
                if (latestJobId > 0) {
                    const newJobData = "Updated job data by owner";
                    await jobExecutor.connect(owner).updateJobData(latestJobId, newJobData);
                    const updatedJob = await jobExecutor.getJobExecution(latestJobId);
                    console.log("   Job data updated:", updatedJob.jobData === newJobData);
                }
            } else {
                console.log("⚠️  Current account is not the owner, skipping owner tests");
            }
        } catch (error) {
            console.log("⚠️  Owner function test failed:", error.message);
        }

        // Final summary
        console.log("\n🎉 Test Summary");
        const finalStats = await jobExecutor.getContractStats();
        console.log("─".repeat(60));
        console.log("✅ All tests completed successfully!");
        console.log("📊 Final contract state:");
        console.log("   Total jobs executed:", finalStats.totalJobs.toString());
        console.log("   Current job ID:", finalStats.currentJobId.toString());
        console.log("   Job execution enabled:", finalStats.executionEnabled);
        console.log("   Contract address:", CONTRACT_ADDRESS);

    } catch (error) {
        console.error("❌ Test failed:", error.message);

        if (error.code === 'CALL_EXCEPTION') {
            console.error("💡 Possible causes:");
            console.error("   - Contract doesn't exist at this address");
            console.error("   - Wrong network (make sure you're on the same network where contract was deployed)");
            console.error("   - Contract ABI mismatch");
        }
    }
}

main()
    .then(() => {
        console.log("\n🏁 Testing completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("💥 Testing failed:", error);
        process.exit(1);
    });
