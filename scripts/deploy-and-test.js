const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 Deploy and Test JobExecutor Contract");
    console.log("═".repeat(60));

    // Get signers
    const [owner, user1, user2] = await ethers.getSigners();
    console.log("Owner:", owner.address);
    console.log("User1:", user1.address);
    console.log("User2:", user2.address);

    // Deploy contract
    console.log("\n📦 Deploying JobExecutor...");
    const JobExecutor = await ethers.getContractFactory("JobExecutor");
    const jobExecutor = await JobExecutor.deploy(owner.address);
    await jobExecutor.waitForDeployment();
    
    const contractAddress = await jobExecutor.getAddress();
    console.log("✅ Contract deployed at:", contractAddress);

    // Start testing
    console.log("\n🧪 Starting comprehensive tests...");
    console.log("─".repeat(60));

    try {
        // Test 1: Initial state
        console.log("\n📊 Test 1: Initial Contract State");
        let stats = await jobExecutor.getContractStats();
        console.log("✅ Initial total jobs:", stats.totalJobs.toString());
        console.log("✅ Initial job ID:", stats.currentJobId.toString());
        console.log("✅ Execution enabled:", stats.executionEnabled);
        console.log("✅ Contract owner:", await jobExecutor.owner());

        // Test 2: Execute simple jobs
        console.log("\n🚀 Test 2: Execute Simple Jobs");
        const jobs = [
            "Process user data",
            "Generate monthly report", 
            "Backup database",
            "Send notifications"
        ];

        for (let i = 0; i < jobs.length; i++) {
            const tx = await jobExecutor.connect(i % 2 === 0 ? user1 : user2).executeJob(jobs[i]);
            await tx.wait();
            console.log(`✅ Job ${i + 1} executed: "${jobs[i]}"`);
        }

        // Test 3: Execute calculation jobs
        console.log("\n🧮 Test 3: Execute Calculation Jobs");
        const inputs = [5, 10, 15];
        for (const input of inputs) {
            const tx = await jobExecutor.connect(user1).executeCalculationJob(input);
            await tx.wait();
            console.log(`✅ Calculation job executed with input: ${input}`);
        }

        // Test 4: Execute batch jobs
        console.log("\n📦 Test 4: Execute Batch Jobs");
        const batchJobs = [
            "Batch: Validate input data",
            "Batch: Process transactions",
            "Batch: Update user balances"
        ];
        const tx = await jobExecutor.connect(user2).executeBatchJobs(batchJobs);
        await tx.wait();
        console.log(`✅ Batch of ${batchJobs.length} jobs executed`);

        // Test 5: Check updated stats
        console.log("\n📈 Test 5: Updated Contract Stats");
        stats = await jobExecutor.getContractStats();
        console.log("✅ Total jobs executed:", stats.totalJobs.toString());
        console.log("✅ Current job ID:", stats.currentJobId.toString());

        // Test 6: Get job details
        console.log("\n🔍 Test 6: Job Execution Details");
        const totalJobs = Number(stats.currentJobId);
        if (totalJobs > 0) {
            // Check first job
            const firstJob = await jobExecutor.getJobExecution(1);
            console.log("✅ First job details:");
            console.log("   Job ID:", firstJob.jobId.toString());
            console.log("   Executor:", firstJob.executor);
            console.log("   Data:", firstJob.jobData);

            // Check last job
            const lastJob = await jobExecutor.getJobExecution(totalJobs);
            console.log("✅ Last job details:");
            console.log("   Job ID:", lastJob.jobId.toString());
            console.log("   Executor:", lastJob.executor);
            console.log("   Data:", lastJob.jobData);
        }

        // Test 7: User job history
        console.log("\n📚 Test 7: User Job History");
        const user1Jobs = await jobExecutor.getUserJobHistory(user1.address, 10);
        const user2Jobs = await jobExecutor.getUserJobHistory(user2.address, 10);
        console.log(`✅ User1 executed ${user1Jobs.length} jobs`);
        console.log(`✅ User2 executed ${user2Jobs.length} jobs`);

        // Show user1's jobs
        if (user1Jobs.length > 0) {
            console.log("   User1's jobs:");
            user1Jobs.forEach((job, index) => {
                console.log(`   ${index + 1}. Job ${job.jobId}: "${job.jobData}"`);
            });
        }

        // Test 8: Latest jobs
        console.log("\n🕐 Test 8: Latest Jobs");
        const latestJobs = await jobExecutor.getLatestJobs(5);
        console.log(`✅ Retrieved ${latestJobs.length} latest jobs:`);
        latestJobs.forEach((job, index) => {
            console.log(`   ${index + 1}. Job ${job.jobId}: "${job.jobData}"`);
        });

        // Test 9: Job existence checks
        console.log("\n✔️  Test 9: Job Existence Checks");
        const checks = [1, 5, 10, 999];
        for (const jobId of checks) {
            const exists = await jobExecutor.isJobExecuted(jobId);
            console.log(`✅ Job ${jobId} exists: ${exists}`);
        }

        // Test 10: Owner functions
        console.log("\n👑 Test 10: Owner Functions");
        
        // Toggle job execution
        console.log("   Testing toggle job execution...");
        await jobExecutor.connect(owner).toggleJobExecution();
        let enabled = await jobExecutor.jobExecutionEnabled();
        console.log(`   ✅ Job execution disabled: ${!enabled}`);
        
        // Try to execute job when disabled (should fail)
        try {
            await jobExecutor.connect(user1).executeJob("This should fail");
            console.log("   ❌ Job executed when disabled (unexpected)");
        } catch (error) {
            console.log("   ✅ Job execution correctly blocked when disabled");
        }
        
        // Re-enable
        await jobExecutor.connect(owner).toggleJobExecution();
        enabled = await jobExecutor.jobExecutionEnabled();
        console.log(`   ✅ Job execution re-enabled: ${enabled}`);

        // Update job data
        if (totalJobs > 0) {
            console.log("   Testing update job data...");
            const newData = "Updated by owner - Test completed";
            await jobExecutor.connect(owner).updateJobData(1, newData);
            const updatedJob = await jobExecutor.getJobExecution(1);
            console.log(`   ✅ Job 1 data updated: "${updatedJob.jobData}"`);
        }

        // Test 11: Error handling
        console.log("\n⚠️  Test 11: Error Handling");
        
        // Empty job data
        try {
            await jobExecutor.connect(user1).executeJob("");
            console.log("   ❌ Empty job data accepted (unexpected)");
        } catch (error) {
            console.log("   ✅ Empty job data correctly rejected");
        }

        // Zero calculation input
        try {
            await jobExecutor.connect(user1).executeCalculationJob(0);
            console.log("   ❌ Zero calculation input accepted (unexpected)");
        } catch (error) {
            console.log("   ✅ Zero calculation input correctly rejected");
        }

        // Non-owner trying owner functions
        try {
            await jobExecutor.connect(user1).toggleJobExecution();
            console.log("   ❌ Non-owner accessed owner function (unexpected)");
        } catch (error) {
            console.log("   ✅ Non-owner correctly blocked from owner functions");
        }

        // Final summary
        console.log("\n🎉 Test Summary");
        const finalStats = await jobExecutor.getContractStats();
        console.log("═".repeat(60));
        console.log("✅ ALL TESTS PASSED SUCCESSFULLY!");
        console.log("📊 Final contract state:");
        console.log("   Contract address:", contractAddress);
        console.log("   Total jobs executed:", finalStats.totalJobs.toString());
        console.log("   Current job ID:", finalStats.currentJobId.toString());
        console.log("   Job execution enabled:", finalStats.executionEnabled);
        console.log("   Contract owner:", await jobExecutor.owner());
        console.log("═".repeat(60));

        return contractAddress;

    } catch (error) {
        console.error("❌ Test failed:", error.message);
        throw error;
    }
}

main()
    .then((contractAddress) => {
        console.log(`\n🏆 Success! Contract deployed and tested at: ${contractAddress}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error("💥 Deploy and test failed:", error);
        process.exit(1);
    });
