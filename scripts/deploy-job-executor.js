const { ethers } = require("hardhat");

async function main() {
    console.log("Deploying JobExecutor contract...");

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));

    // Deploy the contract
    const JobExecutor = await ethers.getContractFactory("JobExecutor");
    const jobExecutor = await JobExecutor.deploy(deployer.address);
    
    await jobExecutor.waitForDeployment();
    const contractAddress = await jobExecutor.getAddress();

    console.log("JobExecutor deployed to:", contractAddress);
    console.log("Owner:", await jobExecutor.owner());
    console.log("Job execution enabled:", await jobExecutor.jobExecutionEnabled());
    console.log("Initial job counter:", await jobExecutor.jobCounter());

    // Test execute a job
    console.log("\nTesting job execution...");
    const tx = await jobExecutor.executeJob("Test deployment job");
    await tx.wait();
    
    console.log("Job executed successfully!");
    console.log("Job counter after test:", await jobExecutor.jobCounter());
    console.log("Total jobs executed:", await jobExecutor.totalJobsExecuted());

    // Get the executed job details
    const jobExecution = await jobExecutor.getJobExecution(1);
    console.log("\nJob execution details:");
    console.log("- Job ID:", jobExecution.jobId.toString());
    console.log("- Executor:", jobExecution.executor);
    console.log("- Job Data:", jobExecution.jobData);
    console.log("- Timestamp:", new Date(Number(jobExecution.timestamp) * 1000).toISOString());

    return contractAddress;
}

main()
    .then((contractAddress) => {
        console.log(`\nDeployment completed successfully!`);
        console.log(`Contract address: ${contractAddress}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error("Deployment failed:", error);
        process.exit(1);
    });
