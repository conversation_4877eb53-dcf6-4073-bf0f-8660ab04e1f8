const { ethers } = require("hardhat");

async function main() {
    console.log("🔍 Tracing DTC Token Flow in Job Reward System");
    console.log("═".repeat(70));

    // Get signers (these are the "workers" and other participants)
    const [deployer, creator, worker1, worker2, randomUser] = await ethers.getSigners();
    
    console.log("👥 Participants:");
    console.log("   Deployer (Contract Owner):", deployer.address);
    console.log("   Creator (Job Creator):", creator.address);
    console.log("   Worker1 (Job Worker):", worker1.address);
    console.log("   Worker2 (Job Worker):", worker2.address);
    console.log("   Random User:", randomUser.address);

    // Deploy contracts
    console.log("\n📦 Deploying contracts...");
    
    // Deploy DATACOIN
    const DATACOIN = await ethers.getContractFactory("DATACOIN");
    const datacoin = await DATACOIN.deploy(deployer.address);
    await datacoin.waitForDeployment();
    const datacoinAddress = await datacoin.getAddress();
    
    // Deploy JobRewardSystem
    const minimumReward = ethers.parseEther("10");
    const JobRewardSystem = await ethers.getContractFactory("JobRewardSystem");
    const jobRewardSystem = await JobRewardSystem.deploy(
        deployer.address,
        datacoinAddress,
        minimumReward
    );
    await jobRewardSystem.waitForDeployment();
    const jobSystemAddress = await jobRewardSystem.getAddress();

    console.log("✅ DATACOIN deployed at:", datacoinAddress);
    console.log("✅ JobRewardSystem deployed at:", jobSystemAddress);

    // Initial token distribution
    console.log("\n💰 Initial Token Distribution:");
    const initialSupply = ethers.parseEther("1000");
    
    await datacoin.mint(creator.address, initialSupply);
    console.log(`✅ Minted ${ethers.formatEther(initialSupply)} DTC to Creator`);
    
    // Show initial balances
    console.log("\n📊 Initial Balances:");
    const balances = {};
    for (const [name, signer] of [
        ["Deployer", deployer],
        ["Creator", creator], 
        ["Worker1", worker1],
        ["Worker2", worker2],
        ["Random User", randomUser],
        ["JobSystem Contract", { address: jobSystemAddress }]
    ]) {
        const balance = await datacoin.balanceOf(signer.address);
        balances[name] = balance;
        console.log(`   ${name}: ${ethers.formatEther(balance)} DTC`);
    }

    // Approve JobRewardSystem to spend creator's tokens
    const approvalAmount = ethers.parseEther("500");
    await datacoin.connect(creator).approve(jobSystemAddress, approvalAmount);
    console.log(`\n🔐 Creator approved ${ethers.formatEther(approvalAmount)} DTC for JobRewardSystem`);

    // Step 1: Create Job (DTC flows from Creator to Contract)
    console.log("\n" + "=".repeat(50));
    console.log("STEP 1: CREATE JOB - DTC FLOW ANALYSIS");
    console.log("=".repeat(50));
    
    const jobReward = ethers.parseEther("50");
    console.log(`📝 Creating job with ${ethers.formatEther(jobReward)} DTC reward...`);
    
    console.log("\n💸 DTC Flow: Creator → JobSystem Contract");
    console.log("   Before job creation:");
    const creatorBalanceBefore = await datacoin.balanceOf(creator.address);
    const contractBalanceBefore = await datacoin.balanceOf(jobSystemAddress);
    console.log(`   Creator balance: ${ethers.formatEther(creatorBalanceBefore)} DTC`);
    console.log(`   Contract balance: ${ethers.formatEther(contractBalanceBefore)} DTC`);
    
    await jobRewardSystem.connect(creator).createJob("Test job: Data processing", jobReward);
    
    console.log("   After job creation:");
    const creatorBalanceAfter = await datacoin.balanceOf(creator.address);
    const contractBalanceAfter = await datacoin.balanceOf(jobSystemAddress);
    console.log(`   Creator balance: ${ethers.formatEther(creatorBalanceAfter)} DTC`);
    console.log(`   Contract balance: ${ethers.formatEther(contractBalanceAfter)} DTC`);
    
    const creatorDecrease = creatorBalanceBefore - creatorBalanceAfter;
    const contractIncrease = contractBalanceAfter - contractBalanceBefore;
    console.log(`\n✅ DTC Transfer Verified:`);
    console.log(`   Creator lost: ${ethers.formatEther(creatorDecrease)} DTC`);
    console.log(`   Contract gained: ${ethers.formatEther(contractIncrease)} DTC`);
    console.log(`   Transfer successful: ${creatorDecrease === contractIncrease && creatorDecrease === jobReward}`);

    // Step 2: Assign and Complete Job (DTC flows from Contract to Worker)
    console.log("\n" + "=".repeat(50));
    console.log("STEP 2: COMPLETE JOB - DTC FLOW ANALYSIS");
    console.log("=".repeat(50));
    
    console.log("📋 Assigning job to Worker1...");
    await jobRewardSystem.connect(creator).assignJob(1, worker1.address);
    
    console.log("🚀 Worker1 starting job...");
    await jobRewardSystem.connect(worker1).startJob(1);
    
    console.log(`💰 Completing job - DTC flows from Contract to Worker1...`);
    console.log("\n💸 DTC Flow: JobSystem Contract → Worker1");
    console.log("   Before job completion:");
    const worker1BalanceBefore = await datacoin.balanceOf(worker1.address);
    const contractBalanceBeforeComplete = await datacoin.balanceOf(jobSystemAddress);
    console.log(`   Worker1 balance: ${ethers.formatEther(worker1BalanceBefore)} DTC`);
    console.log(`   Contract balance: ${ethers.formatEther(contractBalanceBeforeComplete)} DTC`);
    
    await jobRewardSystem.connect(worker1).completeJob(1);
    
    console.log("   After job completion:");
    const worker1BalanceAfter = await datacoin.balanceOf(worker1.address);
    const contractBalanceAfterComplete = await datacoin.balanceOf(jobSystemAddress);
    console.log(`   Worker1 balance: ${ethers.formatEther(worker1BalanceAfter)} DTC`);
    console.log(`   Contract balance: ${ethers.formatEther(contractBalanceAfterComplete)} DTC`);
    
    const worker1Increase = worker1BalanceAfter - worker1BalanceBefore;
    const contractDecrease = contractBalanceBeforeComplete - contractBalanceAfterComplete;
    console.log(`\n✅ DTC Transfer Verified:`);
    console.log(`   Worker1 gained: ${ethers.formatEther(worker1Increase)} DTC`);
    console.log(`   Contract lost: ${ethers.formatEther(contractDecrease)} DTC`);
    console.log(`   Transfer successful: ${worker1Increase === contractDecrease && worker1Increase === jobReward}`);

    // Step 3: Show final balances
    console.log("\n" + "=".repeat(50));
    console.log("FINAL BALANCE SUMMARY");
    console.log("=".repeat(50));
    
    console.log("📊 Final Balances (compared to initial):");
    for (const [name, signer] of [
        ["Creator", creator], 
        ["Worker1", worker1],
        ["Worker2", worker2],
        ["JobSystem Contract", { address: jobSystemAddress }]
    ]) {
        const currentBalance = await datacoin.balanceOf(signer.address);
        const initialBalance = balances[name] || 0n;
        const change = currentBalance - initialBalance;
        const changeStr = change >= 0 ? `+${ethers.formatEther(change)}` : ethers.formatEther(change);
        
        console.log(`   ${name}:`);
        console.log(`     Current: ${ethers.formatEther(currentBalance)} DTC`);
        console.log(`     Change: ${changeStr} DTC`);
    }

    // Step 4: Demonstrate Worker1 can use their DTC
    console.log("\n" + "=".repeat(50));
    console.log("STEP 3: WORKER USES EARNED DTC");
    console.log("=".repeat(50));
    
    console.log("💳 Worker1 can now use their earned DTC...");
    
    // Worker1 transfers some DTC to Worker2
    const transferAmount = ethers.parseEther("20");
    console.log(`📤 Worker1 transfers ${ethers.formatEther(transferAmount)} DTC to Worker2...`);
    
    const worker2BalanceBefore = await datacoin.balanceOf(worker2.address);
    await datacoin.connect(worker1).transfer(worker2.address, transferAmount);
    const worker2BalanceAfter = await datacoin.balanceOf(worker2.address);
    const worker1FinalBalance = await datacoin.balanceOf(worker1.address);
    
    console.log("✅ Transfer completed:");
    console.log(`   Worker1 remaining: ${ethers.formatEther(worker1FinalBalance)} DTC`);
    console.log(`   Worker2 received: ${ethers.formatEther(worker2BalanceAfter - worker2BalanceBefore)} DTC`);
    console.log(`   Worker2 total: ${ethers.formatEther(worker2BalanceAfter)} DTC`);

    // Step 5: Show where DTC can go
    console.log("\n" + "=".repeat(50));
    console.log("WHERE CAN EARNED DTC GO?");
    console.log("=".repeat(50));
    
    console.log("💰 Workers can use their earned DTC for:");
    console.log("   1. 💸 Transfer to other users (as demonstrated above)");
    console.log("   2. 🛒 Create their own jobs (become job creators)");
    console.log("   3. 🏪 Trade on exchanges (if listed)");
    console.log("   4. 💎 Hold as investment");
    console.log("   5. 🔥 Burn tokens (if they want to reduce supply)");
    console.log("   6. 🎮 Use in other DApps that accept DTC");
    
    // Demonstrate Worker1 creating a job with earned DTC
    console.log("\n🔄 Demonstrating: Worker1 creates a job with earned DTC...");
    
    // Worker1 approves JobRewardSystem to spend their DTC
    await datacoin.connect(worker1).approve(jobSystemAddress, ethers.parseEther("30"));
    
    // Worker1 creates a job
    const newJobReward = ethers.parseEther("25");
    await jobRewardSystem.connect(worker1).createJob("Job created by Worker1", newJobReward);
    
    console.log(`✅ Worker1 created a job with ${ethers.formatEther(newJobReward)} DTC reward`);
    console.log("   Worker1 is now both a worker AND a job creator!");
    
    const worker1NewBalance = await datacoin.balanceOf(worker1.address);
    console.log(`   Worker1 balance after creating job: ${ethers.formatEther(worker1NewBalance)} DTC`);

    console.log("\n🎯 Summary:");
    console.log("═".repeat(70));
    console.log("🔄 DTC Token Flow:");
    console.log("   1. Creator → Contract (when creating job)");
    console.log("   2. Contract → Worker (when completing job)");
    console.log("   3. Worker → Anyone (worker can freely use earned DTC)");
    console.log("");
    console.log("👤 Workers are:");
    console.log("   - Any Ethereum wallet address");
    console.log("   - Assigned by job creators");
    console.log("   - Receive DTC directly to their wallet");
    console.log("   - Can use DTC for anything they want");
    console.log("═".repeat(70));
}

main()
    .then(() => {
        console.log("\n🏆 DTC Flow Analysis Complete!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("💥 Analysis failed:", error);
        process.exit(1);
    });
