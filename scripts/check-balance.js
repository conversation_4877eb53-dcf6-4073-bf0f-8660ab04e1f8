async function main() {
  console.log("💰 Checking Account Balance");
  console.log("=" .repeat(30));
  
  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log("📋 Account Details:");
  console.log("  Address:", deployer.address);
  console.log("  Network:", network.name, "(Chain ID:", network.chainId.toString() + ")");
  
  // Get native balance
  const balance = await ethers.provider.getBalance(deployer.address);
  const balanceEth = ethers.formatEther(balance);
  
  console.log("\n💰 Balance Information:");
  console.log("  Native Balance:", balanceEth, "DTC");
  console.log("  Balance (Wei):", balance.toString());
  
  // Estimate gas cost for deployment
  const gasPrice = await ethers.provider.getFeeData();
  const estimatedGasCost = 2000000n * (gasPrice.gasPrice || 20000000000n); // 2M gas * gas price
  const estimatedCostEth = ethers.formatEther(estimatedGasCost);
  
  console.log("\n⛽ Gas Estimation:");
  console.log("  Current Gas Price:", ethers.formatUnits(gasPrice.gasPrice || 0n, "gwei"), "Gwei");
  console.log("  Estimated Gas Limit: 2,000,000");
  console.log("  Estimated Cost:", estimatedCostEth, "DTC");
  
  // Check if sufficient funds
  const hasSufficientFunds = balance >= estimatedGasCost;
  
  console.log("\n✅ Fund Check:");
  if (hasSufficientFunds) {
    console.log("  Status: ✅ Sufficient funds for deployment");
    console.log("  You can proceed with deployment");
  } else {
    console.log("  Status: ❌ Insufficient funds");
    console.log("  Required:", estimatedCostEth, "DTC");
    console.log("  Current:", balanceEth, "DTC");
    console.log("  Needed:", ethers.formatEther(estimatedGasCost - balance), "DTC more");
    
    console.log("\n💡 Solutions:");
    console.log("  1. Send DTC to your deployer address:", deployer.address);
    console.log("  2. Use a different account with DTC balance");
    console.log("  3. Get DTC from faucet (if available)");
    console.log("  4. Mine some DTC if you're running local node");
  }
  
  console.log("\n📝 Next Steps:");
  if (hasSufficientFunds) {
    console.log("  Run: npm run deploy:ethermint");
  } else {
    console.log("  1. Fund your account with DTC");
    console.log("  2. Run this script again to verify");
    console.log("  3. Then deploy: npm run deploy:ethermint");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("\n❌ Balance check failed:");
    console.error(error);
    process.exitCode = 1;
  });
