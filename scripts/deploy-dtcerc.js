async function main() {
  console.log("🚀 Starting DATACOIN ERC (DTCERC) Token Deployment");
  console.log("=" .repeat(50));

  const [deployer] = await ethers.getSigners();

  console.log("📋 Deployment Details:");
  console.log("  Deployer address:", deployer.address);
  console.log("  Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)), "ETH");

  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("  Network:", network.name, "(Chain ID:", network.chainId.toString() + ")");

  console.log("\n🔨 Deploying DATACOIN ERC Token...");

  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = await DATACOIN.deploy(deployer.address, {
    gasLimit: 2000000,
    gasPrice: ethers.parseUnits("20", "gwei")
  });

  console.log("⏳ Waiting for deployment confirmation...");
  await token.waitForDeployment();

  const contractAddress = await token.getAddress();
  const deploymentTx = token.deploymentTransaction();

  console.log("\n✅ Deployment Successful!");
  console.log("=" .repeat(50));
  console.log("📄 Contract Address:", contractAddress);
  console.log("🔗 Transaction Hash:", deploymentTx.hash);
  console.log("⛽ Gas Used:", deploymentTx.gasLimit.toString());

  // Verify token details
  console.log("\n📊 Token Information:");
  console.log("  Name:", await token.name());
  console.log("  Symbol:", await token.symbol());
  console.log("  Decimals:", await token.decimals());
  console.log("  Owner:", await token.owner());
  console.log("  Total Supply:", ethers.formatEther(await token.totalSupply()), "DTCERC");

  // Save deployment info
  const deploymentInfo = {
    contractAddress: contractAddress,
    transactionHash: deploymentTx.hash,
    deployer: deployer.address,
    network: {
      name: network.name,
      chainId: network.chainId.toString()
    },
    token: {
      name: await token.name(),
      symbol: await token.symbol(),
      decimals: await token.decimals(),
      totalSupply: await token.totalSupply()
    },
    timestamp: new Date().toISOString()
  };

  console.log("\n💾 Deployment info saved to deployed_addresses.json");

  // Instructions for next steps
  console.log("\n📝 Next Steps:");
  console.log("1. Update your frontend .env.local file:");
  console.log(`   NEXT_PUBLIC_CONTRACT_ADDRESS=${contractAddress}`);
  console.log("2. Add token to MetaMask:");
  console.log(`   - Contract Address: ${contractAddress}`);
  console.log(`   - Token Symbol: DTCERC`);
  console.log(`   - Decimals: 18`);
  console.log("3. Mint some tokens for testing:");
  console.log(`   npx hardhat run scripts/mint-tokens.js --network <your-network>`);

  console.log("\n🎉 DATACOIN ERC (DTCERC) deployment completed successfully!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("\n❌ Deployment failed:");
    console.error(error);
    process.exitCode = 1;
  });
