const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("DatacoinModule", (m) => {
  // Lấy deployer address từ parameters hoặc sử dụng default
  const owner = m.getParameter("owner", "******************************************");

  // Deploy DATACOIN contract
  const datacoin = m.contract("DATACOIN", [owner], {
    // Cấu hình gas cho deployment
    gasLimit: 2000000,
  });

  // Mint initial supply cho owner (nếu contract có function mint)
  const initialSupply = m.getParameter("initialSupply", "1000000"); // 1 triệu token
  const mintAmount = m.staticCall(datacoin, "parseEther", [initialSupply]);

  // Gọi mint function sau khi deploy (nếu có)
  m.call(datacoin, "mint", [owner, mintAmount], {
    id: "mint_initial_supply",
    gasLimit: 200000,
  });

  // Return contract instance để có thể sử dụng trong các module khác
  return { datacoin };
});
