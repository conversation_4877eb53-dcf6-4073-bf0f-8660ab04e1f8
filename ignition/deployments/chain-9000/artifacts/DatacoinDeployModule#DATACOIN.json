{"_format": "hh-sol-artifact-1", "contractName": "DATACOIN", "sourceName": "contracts/DATACOIN.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}], "name": "getLastTransferAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "reverseTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferWithTracking", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}