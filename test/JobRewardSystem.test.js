const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("JobRewardSystem", function () {
    let DATACOIN;
    let datacoin;
    let JobRewardSystem;
    let jobRewardSystem;
    let owner;
    let creator;
    let worker1;
    let worker2;

    const MINIMUM_REWARD = ethers.parseEther("10"); // 10 DTC
    const JOB_REWARD = ethers.parseEther("50"); // 50 DTC
    const INITIAL_SUPPLY = ethers.parseEther("10000"); // 10,000 DTC

    beforeEach(async function () {
        // Get signers
        [owner, creator, worker1, worker2] = await ethers.getSigners();

        // Deploy DATACOIN token
        DATACOIN = await ethers.getContractFactory("DATACOIN");
        datacoin = await DATACOIN.deploy(owner.address);
        await datacoin.waitForDeployment();

        // Mint tokens for testing
        await datacoin.mint(creator.address, INITIAL_SUPPLY);
        await datacoin.mint(owner.address, INITIAL_SUPPLY);

        // Deploy JobRewardSystem
        JobRewardSystem = await ethers.getContractFactory("JobRewardSystem");
        jobRewardSystem = await JobRewardSystem.deploy(
            owner.address,
            await datacoin.getAddress(),
            MINIMUM_REWARD
        );
        await jobRewardSystem.waitForDeployment();

        // Approve JobRewardSystem to spend creator's tokens
        await datacoin.connect(creator).approve(
            await jobRewardSystem.getAddress(),
            ethers.parseEther("1000")
        );
    });

    describe("Deployment", function () {
        it("Should set the right parameters", async function () {
            expect(await jobRewardSystem.owner()).to.equal(owner.address);
            expect(await jobRewardSystem.datacoinToken()).to.equal(await datacoin.getAddress());
            expect(await jobRewardSystem.minimumReward()).to.equal(MINIMUM_REWARD);
            expect(await jobRewardSystem.systemEnabled()).to.equal(true);
        });
    });

    describe("Job Creation and Reward Payment", function () {
        it("Should create job and pay reward when completed", async function () {
            const jobDescription = "Test job for DTC reward";
            
            // Check initial balances
            const creatorInitialBalance = await datacoin.balanceOf(creator.address);
            const worker1InitialBalance = await datacoin.balanceOf(worker1.address);
            
            console.log("Creator initial balance:", ethers.formatEther(creatorInitialBalance), "DTC");
            console.log("Worker1 initial balance:", ethers.formatEther(worker1InitialBalance), "DTC");

            // Step 1: Create job
            const tx1 = await jobRewardSystem.connect(creator).createJob(jobDescription, JOB_REWARD);
            await tx1.wait();
            
            console.log("✅ Job created with reward:", ethers.formatEther(JOB_REWARD), "DTC");

            // Check job was created
            const job = await jobRewardSystem.getJob(1);
            expect(job.creator).to.equal(creator.address);
            expect(job.description).to.equal(jobDescription);
            expect(job.reward).to.equal(JOB_REWARD);
            expect(job.status).to.equal(0); // CREATED

            // Check creator's balance decreased
            const creatorBalanceAfterCreate = await datacoin.balanceOf(creator.address);
            expect(creatorBalanceAfterCreate).to.equal(creatorInitialBalance - JOB_REWARD);
            console.log("Creator balance after job creation:", ethers.formatEther(creatorBalanceAfterCreate), "DTC");

            // Step 2: Assign job to worker
            const tx2 = await jobRewardSystem.connect(creator).assignJob(1, worker1.address);
            await tx2.wait();
            
            console.log("✅ Job assigned to worker1");

            // Check job status
            const assignedJob = await jobRewardSystem.getJob(1);
            expect(assignedJob.assignee).to.equal(worker1.address);
            expect(assignedJob.status).to.equal(1); // ASSIGNED

            // Step 3: Worker starts job
            const tx3 = await jobRewardSystem.connect(worker1).startJob(1);
            await tx3.wait();
            
            console.log("✅ Worker1 started the job");

            // Check job status
            const startedJob = await jobRewardSystem.getJob(1);
            expect(startedJob.status).to.equal(2); // IN_PROGRESS

            // Step 4: Worker completes job and receives reward
            const tx4 = await jobRewardSystem.connect(worker1).completeJob(1);
            const receipt = await tx4.wait();
            
            console.log("✅ Worker1 completed the job");

            // Check job status
            const completedJob = await jobRewardSystem.getJob(1);
            expect(completedJob.status).to.equal(3); // COMPLETED
            expect(completedJob.rewardPaid).to.equal(true);
            expect(completedJob.completedAt).to.be.greaterThan(0);

            // Check worker received reward
            const worker1FinalBalance = await datacoin.balanceOf(worker1.address);
            expect(worker1FinalBalance).to.equal(worker1InitialBalance + JOB_REWARD);
            console.log("Worker1 final balance:", ethers.formatEther(worker1FinalBalance), "DTC");
            console.log("Worker1 earned:", ethers.formatEther(JOB_REWARD), "DTC");

            // Check events were emitted
            const events = receipt.logs.filter(log => {
                try {
                    const parsed = jobRewardSystem.interface.parseLog(log);
                    return parsed.name === 'JobCompleted' || parsed.name === 'RewardPaid';
                } catch {
                    return false;
                }
            });
            expect(events.length).to.be.greaterThan(0);

            // Check user stats
            const workerStats = await jobRewardSystem.getUserStats(worker1.address);
            expect(workerStats.totalEarned).to.equal(JOB_REWARD);
            expect(workerStats.jobsAssigned).to.equal(1);

            const creatorStats = await jobRewardSystem.getUserStats(creator.address);
            expect(creatorStats.totalSpent).to.equal(JOB_REWARD);
            expect(creatorStats.jobsCreated).to.equal(1);

            console.log("🎉 Job completed successfully and reward paid!");
        });

        it("Should handle multiple jobs and payments", async function () {
            const jobs = [
                { description: "Job 1: Data processing", reward: ethers.parseEther("30") },
                { description: "Job 2: File validation", reward: ethers.parseEther("40") },
                { description: "Job 3: Report generation", reward: ethers.parseEther("50") }
            ];

            console.log("Creating and completing multiple jobs...");

            for (let i = 0; i < jobs.length; i++) {
                const job = jobs[i];
                const worker = i % 2 === 0 ? worker1 : worker2;
                
                console.log(`\n--- Job ${i + 1} ---`);
                console.log("Description:", job.description);
                console.log("Reward:", ethers.formatEther(job.reward), "DTC");
                console.log("Worker:", worker.address);

                // Create job
                await jobRewardSystem.connect(creator).createJob(job.description, job.reward);
                
                // Assign job
                await jobRewardSystem.connect(creator).assignJob(i + 1, worker.address);
                
                // Start job
                await jobRewardSystem.connect(worker).startJob(i + 1);
                
                // Complete job
                const workerBalanceBefore = await datacoin.balanceOf(worker.address);
                await jobRewardSystem.connect(worker).completeJob(i + 1);
                const workerBalanceAfter = await datacoin.balanceOf(worker.address);
                
                const earned = workerBalanceAfter - workerBalanceBefore;
                console.log("Worker earned:", ethers.formatEther(earned), "DTC");
                
                expect(earned).to.equal(job.reward);
            }

            // Check final stats
            const systemStats = await jobRewardSystem.getSystemStats();
            console.log("\n📊 Final System Stats:");
            console.log("Total jobs:", systemStats.totalJobs.toString());
            console.log("Total rewards paid:", ethers.formatEther(systemStats.totalRewards), "DTC");

            const worker1Stats = await jobRewardSystem.getUserStats(worker1.address);
            const worker2Stats = await jobRewardSystem.getUserStats(worker2.address);
            
            console.log("\n👤 Worker Stats:");
            console.log("Worker1 total earned:", ethers.formatEther(worker1Stats.totalEarned), "DTC");
            console.log("Worker2 total earned:", ethers.formatEther(worker2Stats.totalEarned), "DTC");

            expect(systemStats.totalJobs).to.equal(3);
        });

        it("Should fail when trying to complete job twice", async function () {
            // Create and complete a job
            await jobRewardSystem.connect(creator).createJob("Test job", JOB_REWARD);
            await jobRewardSystem.connect(creator).assignJob(1, worker1.address);
            await jobRewardSystem.connect(worker1).startJob(1);
            await jobRewardSystem.connect(worker1).completeJob(1);

            // Try to complete again
            await expect(
                jobRewardSystem.connect(worker1).completeJob(1)
            ).to.be.revertedWith("Job not in progress");
        });

        it("Should allow job cancellation and refund", async function () {
            const creatorBalanceBefore = await datacoin.balanceOf(creator.address);
            
            // Create job
            await jobRewardSystem.connect(creator).createJob("Test job", JOB_REWARD);
            
            const creatorBalanceAfterCreate = await datacoin.balanceOf(creator.address);
            expect(creatorBalanceAfterCreate).to.equal(creatorBalanceBefore - JOB_REWARD);
            
            // Cancel job
            await jobRewardSystem.connect(creator).cancelJob(1);
            
            // Check refund
            const creatorBalanceAfterCancel = await datacoin.balanceOf(creator.address);
            expect(creatorBalanceAfterCancel).to.equal(creatorBalanceBefore);
            
            // Check job status
            const job = await jobRewardSystem.getJob(1);
            expect(job.status).to.equal(4); // CANCELLED
            
            console.log("✅ Job cancelled and refund processed");
        });
    });

    describe("System Controls", function () {
        it("Should allow owner to toggle system", async function () {
            expect(await jobRewardSystem.systemEnabled()).to.equal(true);
            
            await jobRewardSystem.connect(owner).toggleSystem();
            expect(await jobRewardSystem.systemEnabled()).to.equal(false);
            
            // Should fail to create job when disabled
            await expect(
                jobRewardSystem.connect(creator).createJob("Test", JOB_REWARD)
            ).to.be.revertedWith("System is disabled");
        });

        it("Should allow owner to set minimum reward", async function () {
            const newMinimum = ethers.parseEther("20");
            await jobRewardSystem.connect(owner).setMinimumReward(newMinimum);
            expect(await jobRewardSystem.minimumReward()).to.equal(newMinimum);
        });
    });
});
