const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("JobExecutor", function () {
    let JobExecutor;
    let jobExecutor;
    let owner;
    let addr1;
    let addr2;

    beforeEach(async function () {
        // Get signers
        [owner, addr1, addr2] = await ethers.getSigners();

        // Deploy contract
        JobExecutor = await ethers.getContractFactory("JobExecutor");
        jobExecutor = await JobExecutor.deploy(owner.address);
        await jobExecutor.waitForDeployment();
    });

    describe("Deployment", function () {
        it("Should set the right owner", async function () {
            expect(await jobExecutor.owner()).to.equal(owner.address);
        });

        it("Should initialize with correct default values", async function () {
            expect(await jobExecutor.jobCounter()).to.equal(0);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(0);
            expect(await jobExecutor.jobExecutionEnabled()).to.equal(true);
        });
    });

    describe("Job Execution", function () {
        it("Should execute a simple job successfully", async function () {
            const jobData = "Test job data";
            
            // Execute job
            const tx = await jobExecutor.connect(addr1).executeJob(jobData);
            const receipt = await tx.wait();

            // Check if job was executed
            expect(await jobExecutor.jobCounter()).to.equal(1);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(1);
            expect(await jobExecutor.userJobCount(addr1.address)).to.equal(1);

            // Check job execution details
            const jobExecution = await jobExecutor.getJobExecution(1);
            expect(jobExecution.executor).to.equal(addr1.address);
            expect(jobExecution.jobData).to.equal(jobData);
            expect(jobExecution.jobId).to.equal(1);

            // Check if event was emitted
            const events = receipt.logs.filter(log => {
                try {
                    return jobExecutor.interface.parseLog(log).name === 'JobExecuted';
                } catch {
                    return false;
                }
            });
            expect(events.length).to.equal(1);
        });

        it("Should execute calculation job successfully", async function () {
            const input = 5;
            
            const tx = await jobExecutor.connect(addr1).executeCalculationJob(input);
            const receipt = await tx.wait();

            // Check counters
            expect(await jobExecutor.jobCounter()).to.equal(1);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(1);

            // Check job execution
            const jobExecution = await jobExecutor.getJobExecution(1);
            expect(jobExecution.executor).to.equal(addr1.address);
            expect(jobExecution.jobData).to.include("Calculation:");
        });

        it("Should execute batch jobs successfully", async function () {
            const jobDataArray = ["Job 1", "Job 2", "Job 3"];
            
            const tx = await jobExecutor.connect(addr1).executeBatchJobs(jobDataArray);
            await tx.wait();

            // Check counters
            expect(await jobExecutor.jobCounter()).to.equal(3);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(3);
            expect(await jobExecutor.userJobCount(addr1.address)).to.equal(3);

            // Check each job
            for (let i = 1; i <= 3; i++) {
                const jobExecution = await jobExecutor.getJobExecution(i);
                expect(jobExecution.executor).to.equal(addr1.address);
                expect(jobExecution.jobData).to.equal(jobDataArray[i - 1]);
            }
        });

        it("Should fail when job data is empty", async function () {
            await expect(
                jobExecutor.connect(addr1).executeJob("")
            ).to.be.revertedWith("Job data cannot be empty");
        });

        it("Should fail when calculation input is zero", async function () {
            await expect(
                jobExecutor.connect(addr1).executeCalculationJob(0)
            ).to.be.revertedWith("Input must be greater than 0");
        });

        it("Should fail when job execution is disabled", async function () {
            // Disable job execution
            await jobExecutor.connect(owner).toggleJobExecution();
            
            await expect(
                jobExecutor.connect(addr1).executeJob("Test")
            ).to.be.revertedWith("Job execution is disabled");
        });
    });

    describe("Job History and Queries", function () {
        beforeEach(async function () {
            // Execute some jobs for testing
            await jobExecutor.connect(addr1).executeJob("Job 1 by addr1");
            await jobExecutor.connect(addr2).executeJob("Job 2 by addr2");
            await jobExecutor.connect(addr1).executeJob("Job 3 by addr1");
        });

        it("Should return correct job execution details", async function () {
            const job1 = await jobExecutor.getJobExecution(1);
            expect(job1.executor).to.equal(addr1.address);
            expect(job1.jobData).to.equal("Job 1 by addr1");
            expect(job1.jobId).to.equal(1);

            const job2 = await jobExecutor.getJobExecution(2);
            expect(job2.executor).to.equal(addr2.address);
            expect(job2.jobData).to.equal("Job 2 by addr2");
        });

        it("Should return latest jobs correctly", async function () {
            const latestJobs = await jobExecutor.getLatestJobs(2);
            expect(latestJobs.length).to.equal(2);
            expect(latestJobs[0].jobId).to.equal(3); // Most recent
            expect(latestJobs[1].jobId).to.equal(2);
        });

        it("Should return user job history correctly", async function () {
            const addr1Jobs = await jobExecutor.getUserJobHistory(addr1.address, 10);
            expect(addr1Jobs.length).to.equal(2);
            expect(addr1Jobs[0].jobData).to.equal("Job 3 by addr1"); // Most recent first
            expect(addr1Jobs[1].jobData).to.equal("Job 1 by addr1");

            const addr2Jobs = await jobExecutor.getUserJobHistory(addr2.address, 10);
            expect(addr2Jobs.length).to.equal(1);
            expect(addr2Jobs[0].jobData).to.equal("Job 2 by addr2");
        });

        it("Should check if job is executed correctly", async function () {
            expect(await jobExecutor.isJobExecuted(1)).to.equal(true);
            expect(await jobExecutor.isJobExecuted(2)).to.equal(true);
            expect(await jobExecutor.isJobExecuted(3)).to.equal(true);
            expect(await jobExecutor.isJobExecuted(4)).to.equal(false);
            expect(await jobExecutor.isJobExecuted(0)).to.equal(false);
        });
    });

    describe("Owner Functions", function () {
        it("Should toggle job execution", async function () {
            expect(await jobExecutor.jobExecutionEnabled()).to.equal(true);
            
            await jobExecutor.connect(owner).toggleJobExecution();
            expect(await jobExecutor.jobExecutionEnabled()).to.equal(false);
            
            await jobExecutor.connect(owner).toggleJobExecution();
            expect(await jobExecutor.jobExecutionEnabled()).to.equal(true);
        });

        it("Should update job data", async function () {
            await jobExecutor.connect(addr1).executeJob("Original data");
            
            const newData = "Updated data";
            await jobExecutor.connect(owner).updateJobData(1, newData);
            
            const jobExecution = await jobExecutor.getJobExecution(1);
            expect(jobExecution.jobData).to.equal(newData);
        });

        it("Should reset counters", async function () {
            await jobExecutor.connect(addr1).executeJob("Test job");
            expect(await jobExecutor.jobCounter()).to.equal(1);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(1);
            
            await jobExecutor.connect(owner).resetCounters();
            expect(await jobExecutor.jobCounter()).to.equal(0);
            expect(await jobExecutor.totalJobsExecuted()).to.equal(0);
        });

        it("Should fail when non-owner tries to call owner functions", async function () {
            await expect(
                jobExecutor.connect(addr1).toggleJobExecution()
            ).to.be.revertedWithCustomError(jobExecutor, "OwnableUnauthorizedAccount");
        });
    });

    describe("Contract Stats", function () {
        it("Should return correct contract stats", async function () {
            await jobExecutor.connect(addr1).executeJob("Test job");
            
            const stats = await jobExecutor.getContractStats();
            expect(stats.totalJobs).to.equal(1);
            expect(stats.currentJobId).to.equal(1);
            expect(stats.executionEnabled).to.equal(true);
            expect(stats.contractBalance).to.equal(0);
        });
    });
});
