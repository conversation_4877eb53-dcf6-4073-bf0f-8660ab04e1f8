require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

// Ensure PRIVATE_KEY is available
const PRIVATE_KEY = process.env.PRIVATE_KEY;
if (!PRIVATE_KEY) {
  console.error("❌ PRIVATE_KEY not found in .env file");
  console.log("Please add PRIVATE_KEY=0x... to your .env file");
  process.exit(1);
}

module.exports = {
  solidity: "0.8.28",
  networks: {
    localhost: {
      url: "http://127.0.0.1:8545",
      chainId: 31337
    },
    ethermint: {
      url: process.env.ETHERMINT_RPC_URL || "http://127.0.0.1:8545",
      accounts: [PRIVATE_KEY],
      chainId: 9000,
      gasPrice: ***********, // 20 gwei
      gas: 2100000
    }
  },
};
