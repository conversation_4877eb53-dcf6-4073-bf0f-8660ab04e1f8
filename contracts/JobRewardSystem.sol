// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract JobRewardSystem is Ownable, ReentrancyGuard {
    // Interface cho DATACOIN token
    IERC20 public immutable datacoinToken;
    
    // Enum cho trạng thái job
    enum JobStatus {
        CREATED,
        ASSIGNED,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED
    }
    
    // Struct cho Job
    struct Job {
        uint256 jobId;
        address creator;
        address assignee;
        string description;
        uint256 reward;
        JobStatus status;
        uint256 createdAt;
        uint256 completedAt;
        bool rewardPaid;
    }
    
    // State variables
    uint256 public jobCounter;
    uint256 public totalRewardsPaid;
    uint256 public minimumReward;
    bool public systemEnabled;
    
    // Mappings
    mapping(uint256 => Job) public jobs;
    mapping(address => uint256[]) public userCreatedJobs;
    mapping(address => uint256[]) public userAssignedJobs;
    mapping(address => uint256) public userTotalEarned;
    mapping(address => uint256) public userTotalSpent;
    
    // Events
    event JobCreated(
        uint256 indexed jobId,
        address indexed creator,
        string description,
        uint256 reward
    );
    
    event JobAssigned(
        uint256 indexed jobId,
        address indexed assignee
    );
    
    event JobStarted(
        uint256 indexed jobId,
        address indexed assignee
    );
    
    event JobCompleted(
        uint256 indexed jobId,
        address indexed assignee,
        uint256 reward
    );
    
    event JobCancelled(
        uint256 indexed jobId,
        address indexed creator
    );
    
    event RewardPaid(
        uint256 indexed jobId,
        address indexed recipient,
        uint256 amount
    );
    
    event SystemToggled(bool enabled);
    
    constructor(
        address initialOwner,
        address _datacoinToken,
        uint256 _minimumReward
    ) Ownable(initialOwner) {
        datacoinToken = IERC20(_datacoinToken);
        minimumReward = _minimumReward;
        systemEnabled = true;
        jobCounter = 0;
        totalRewardsPaid = 0;
    }
    
    modifier whenSystemEnabled() {
        require(systemEnabled, "System is disabled");
        _;
    }
    
    modifier validJob(uint256 _jobId) {
        require(_jobId > 0 && _jobId <= jobCounter, "Invalid job ID");
        _;
    }
    
    modifier onlyJobCreator(uint256 _jobId) {
        require(jobs[_jobId].creator == msg.sender, "Not job creator");
        _;
    }
    
    modifier onlyJobAssignee(uint256 _jobId) {
        require(jobs[_jobId].assignee == msg.sender, "Not job assignee");
        _;
    }
    
    // Tạo job mới
    function createJob(
        string memory _description,
        uint256 _reward
    ) external whenSystemEnabled nonReentrant returns (uint256) {
        require(bytes(_description).length > 0, "Description cannot be empty");
        require(_reward >= minimumReward, "Reward below minimum");
        
        // Transfer reward từ creator vào contract
        require(
            datacoinToken.transferFrom(msg.sender, address(this), _reward),
            "Reward transfer failed"
        );
        
        jobCounter++;
        
        jobs[jobCounter] = Job({
            jobId: jobCounter,
            creator: msg.sender,
            assignee: address(0),
            description: _description,
            reward: _reward,
            status: JobStatus.CREATED,
            createdAt: block.timestamp,
            completedAt: 0,
            rewardPaid: false
        });
        
        userCreatedJobs[msg.sender].push(jobCounter);
        userTotalSpent[msg.sender] += _reward;
        
        emit JobCreated(jobCounter, msg.sender, _description, _reward);
        
        return jobCounter;
    }
    
    // Assign job cho worker
    function assignJob(uint256 _jobId, address _assignee) 
        external 
        validJob(_jobId) 
        onlyJobCreator(_jobId) 
        whenSystemEnabled 
    {
        Job storage job = jobs[_jobId];
        require(job.status == JobStatus.CREATED, "Job not available");
        require(_assignee != address(0), "Invalid assignee");
        require(_assignee != job.creator, "Creator cannot be assignee");
        
        job.assignee = _assignee;
        job.status = JobStatus.ASSIGNED;
        
        userAssignedJobs[_assignee].push(_jobId);
        
        emit JobAssigned(_jobId, _assignee);
    }
    
    // Worker bắt đầu làm job
    function startJob(uint256 _jobId) 
        external 
        validJob(_jobId) 
        onlyJobAssignee(_jobId) 
        whenSystemEnabled 
    {
        Job storage job = jobs[_jobId];
        require(job.status == JobStatus.ASSIGNED, "Job not assigned to you");
        
        job.status = JobStatus.IN_PROGRESS;
        
        emit JobStarted(_jobId, msg.sender);
    }
    
    // Hoàn thành job và trả reward
    function completeJob(uint256 _jobId) 
        external 
        validJob(_jobId) 
        onlyJobAssignee(_jobId) 
        whenSystemEnabled 
        nonReentrant 
    {
        Job storage job = jobs[_jobId];
        require(job.status == JobStatus.IN_PROGRESS, "Job not in progress");
        require(!job.rewardPaid, "Reward already paid");
        
        // Update job status
        job.status = JobStatus.COMPLETED;
        job.completedAt = block.timestamp;
        job.rewardPaid = true;
        
        // Transfer reward to assignee
        require(
            datacoinToken.transfer(job.assignee, job.reward),
            "Reward payment failed"
        );
        
        // Update statistics
        userTotalEarned[job.assignee] += job.reward;
        totalRewardsPaid += job.reward;
        
        emit JobCompleted(_jobId, job.assignee, job.reward);
        emit RewardPaid(_jobId, job.assignee, job.reward);
    }
    
    // Cancel job (chỉ creator mới được cancel)
    function cancelJob(uint256 _jobId) 
        external 
        validJob(_jobId) 
        onlyJobCreator(_jobId) 
        whenSystemEnabled 
        nonReentrant 
    {
        Job storage job = jobs[_jobId];
        require(
            job.status == JobStatus.CREATED || job.status == JobStatus.ASSIGNED,
            "Cannot cancel job in progress or completed"
        );
        require(!job.rewardPaid, "Reward already paid");
        
        job.status = JobStatus.CANCELLED;
        
        // Refund reward to creator
        require(
            datacoinToken.transfer(job.creator, job.reward),
            "Refund failed"
        );
        
        // Update statistics
        userTotalSpent[job.creator] -= job.reward;
        
        emit JobCancelled(_jobId, job.creator);
    }
    
    // View functions
    function getJob(uint256 _jobId) external view validJob(_jobId) returns (Job memory) {
        return jobs[_jobId];
    }
    
    function getUserCreatedJobs(address _user) external view returns (uint256[] memory) {
        return userCreatedJobs[_user];
    }
    
    function getUserAssignedJobs(address _user) external view returns (uint256[] memory) {
        return userAssignedJobs[_user];
    }
    
    function getAvailableJobs(uint256 _limit) external view returns (Job[] memory) {
        require(_limit > 0, "Limit must be greater than 0");
        
        // Count available jobs
        uint256 availableCount = 0;
        for (uint256 i = 1; i <= jobCounter; i++) {
            if (jobs[i].status == JobStatus.CREATED) {
                availableCount++;
            }
        }
        
        uint256 actualLimit = _limit > availableCount ? availableCount : _limit;
        Job[] memory availableJobs = new Job[](actualLimit);
        
        uint256 found = 0;
        for (uint256 i = jobCounter; i > 0 && found < actualLimit; i--) {
            if (jobs[i].status == JobStatus.CREATED) {
                availableJobs[found] = jobs[i];
                found++;
            }
        }
        
        return availableJobs;
    }
    
    function getSystemStats() external view returns (
        uint256 totalJobs,
        uint256 totalRewards,
        uint256 minReward,
        bool enabled,
        uint256 contractBalance
    ) {
        return (
            jobCounter,
            totalRewardsPaid,
            minimumReward,
            systemEnabled,
            datacoinToken.balanceOf(address(this))
        );
    }
    
    function getUserStats(address _user) external view returns (
        uint256 jobsCreated,
        uint256 jobsAssigned,
        uint256 totalEarned,
        uint256 totalSpent
    ) {
        return (
            userCreatedJobs[_user].length,
            userAssignedJobs[_user].length,
            userTotalEarned[_user],
            userTotalSpent[_user]
        );
    }
    
    // Owner functions
    function toggleSystem() external onlyOwner {
        systemEnabled = !systemEnabled;
        emit SystemToggled(systemEnabled);
    }
    
    function setMinimumReward(uint256 _minimumReward) external onlyOwner {
        minimumReward = _minimumReward;
    }
    
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = datacoinToken.balanceOf(address(this));
        require(balance > 0, "No tokens to withdraw");
        require(datacoinToken.transfer(owner(), balance), "Withdrawal failed");
    }
}
