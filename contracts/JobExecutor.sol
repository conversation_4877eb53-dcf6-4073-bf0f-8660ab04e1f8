// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";

contract JobExecutor is Ownable {
    // Struct để lưu thông tin job
    struct JobExecution {
        address executor;
        uint256 timestamp;
        string jobData;
        uint256 jobId;
    }

    // State variables
    uint256 public jobCounter;
    uint256 public totalJobsExecuted;
    bool public jobExecutionEnabled;

    // Mapping để lưu lịch sử job executions
    mapping(uint256 => JobExecution) public jobExecutions;
    mapping(address => uint256) public userJobCount;

    // Events
    event JobExecuted(
        uint256 indexed jobId,
        address indexed executor,
        string jobData,
        uint256 timestamp
    );

    event JobExecutionToggled(bool enabled);

    event JobDataUpdated(uint256 indexed jobId, string newData);

    constructor(address initialOwner) Ownable(initialOwner) {
        jobExecutionEnabled = true;
        jobCounter = 0;
        totalJobsExecuted = 0;
    }

    // Modifier để check xem job execution có đ<PERSON>ợc enable không
    modifier whenJobExecutionEnabled() {
        require(jobExecutionEnabled, "Job execution is disabled");
        _;
    }

    // Function chính để execute job
    function executeJob(string memory _jobData) external whenJobExecutionEnabled returns (uint256) {
        require(bytes(_jobData).length > 0, "Job data cannot be empty");

        jobCounter++;
        totalJobsExecuted++;

        // Tạo job execution record
        jobExecutions[jobCounter] = JobExecution({
            executor: msg.sender,
            timestamp: block.timestamp,
            jobData: _jobData,
            jobId: jobCounter
        });

        // Tăng counter cho user
        userJobCount[msg.sender]++;

        // Emit event
        emit JobExecuted(jobCounter, msg.sender, _jobData, block.timestamp);

        return jobCounter;
    }

    // Function để execute job với logic tính toán đơn giản
    function executeCalculationJob(uint256 _input) external whenJobExecutionEnabled returns (uint256) {
        require(_input > 0, "Input must be greater than 0");

        jobCounter++;
        totalJobsExecuted++;

        // Thực hiện tính toán đơn giản (ví dụ: bình phương + timestamp)
        uint256 result = (_input * _input) + (block.timestamp % 1000);

        string memory jobData = string(abi.encodePacked("Calculation: ", toString(_input), " -> ", toString(result)));

        // Tạo job execution record
        jobExecutions[jobCounter] = JobExecution({
            executor: msg.sender,
            timestamp: block.timestamp,
            jobData: jobData,
            jobId: jobCounter
        });

        userJobCount[msg.sender]++;

        emit JobExecuted(jobCounter, msg.sender, jobData, block.timestamp);

        return result;
    }

    // Function để batch execute nhiều jobs
    function executeBatchJobs(string[] memory _jobDataArray) external whenJobExecutionEnabled returns (uint256[] memory) {
        require(_jobDataArray.length > 0, "Job array cannot be empty");
        require(_jobDataArray.length <= 10, "Maximum 10 jobs per batch");

        uint256[] memory jobIds = new uint256[](_jobDataArray.length);

        for (uint256 i = 0; i < _jobDataArray.length; i++) {
            require(bytes(_jobDataArray[i]).length > 0, "Job data cannot be empty");

            jobCounter++;
            totalJobsExecuted++;

            jobExecutions[jobCounter] = JobExecution({
                executor: msg.sender,
                timestamp: block.timestamp,
                jobData: _jobDataArray[i],
                jobId: jobCounter
            });

            jobIds[i] = jobCounter;

            emit JobExecuted(jobCounter, msg.sender, _jobDataArray[i], block.timestamp);
        }

        userJobCount[msg.sender] += _jobDataArray.length;

        return jobIds;
    }

    // Owner functions
    function toggleJobExecution() external onlyOwner {
        jobExecutionEnabled = !jobExecutionEnabled;
        emit JobExecutionToggled(jobExecutionEnabled);
    }

    function updateJobData(uint256 _jobId, string memory _newData) external onlyOwner {
        require(_jobId > 0 && _jobId <= jobCounter, "Invalid job ID");
        require(bytes(_newData).length > 0, "New data cannot be empty");

        jobExecutions[_jobId].jobData = _newData;
        emit JobDataUpdated(_jobId, _newData);
    }

    // View functions
    function getJobExecution(uint256 _jobId) external view returns (JobExecution memory) {
        require(_jobId > 0 && _jobId <= jobCounter, "Invalid job ID");
        return jobExecutions[_jobId];
    }

    function getLatestJobs(uint256 _count) external view returns (JobExecution[] memory) {
        require(_count > 0, "Count must be greater than 0");

        uint256 actualCount = _count > jobCounter ? jobCounter : _count;
        JobExecution[] memory latestJobs = new JobExecution[](actualCount);

        for (uint256 i = 0; i < actualCount; i++) {
            latestJobs[i] = jobExecutions[jobCounter - i];
        }

        return latestJobs;
    }

    function getUserJobHistory(address _user, uint256 _limit) external view returns (JobExecution[] memory) {
        require(_limit > 0, "Limit must be greater than 0");

        // Tạo temporary array để lưu jobs của user
        JobExecution[] memory tempJobs = new JobExecution[](_limit);
        uint256 found = 0;

        // Tìm jobs của user từ mới nhất
        for (uint256 i = jobCounter; i > 0 && found < _limit; i--) {
            if (jobExecutions[i].executor == _user) {
                tempJobs[found] = jobExecutions[i];
                found++;
            }
        }

        // Tạo array với đúng size
        JobExecution[] memory userJobs = new JobExecution[](found);
        for (uint256 i = 0; i < found; i++) {
            userJobs[i] = tempJobs[i];
        }

        return userJobs;
    }

    function isJobExecuted(uint256 _jobId) external view returns (bool) {
        return _jobId > 0 && _jobId <= jobCounter;
    }

    // Helper function để convert uint to string
    function toString(uint256 value) internal pure returns (string memory) {
        if (value == 0) {
            return "0";
        }
        uint256 temp = value;
        uint256 digits;
        while (temp != 0) {
            digits++;
            temp /= 10;
        }
        bytes memory buffer = new bytes(digits);
        while (value != 0) {
            digits -= 1;
            buffer[digits] = bytes1(uint8(48 + uint256(value % 10)));
            value /= 10;
        }
        return string(buffer);
    }

    // Function để reset counter (chỉ owner)
    function resetCounters() external onlyOwner {
        jobCounter = 0;
        totalJobsExecuted = 0;
    }

    // Function để get contract stats
    function getContractStats() external view returns (
        uint256 totalJobs,
        uint256 currentJobId,
        bool executionEnabled,
        uint256 contractBalance
    ) {
        return (
            totalJobsExecuted,
            jobCounter,
            jobExecutionEnabled,
            address(this).balance
        );
    }
}
