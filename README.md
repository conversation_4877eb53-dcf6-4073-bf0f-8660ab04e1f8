# 🪙 DATACOIN - Complete Token Ecosystem

A comprehensive blockchain token ecosystem featuring smart contracts, dApp interface, payment processing, and admin management tools.

## 🌟 Features

### 🔗 Smart Contract
- **ERC20 Token**: Full ERC20 compliance with additional features
- **Mintable & Burnable**: Admin-controlled token supply management
- **Transfer Tracking**: Advanced transaction monitoring
- **Ownership Management**: Secure admin controls

### 🌐 Web dApp (Next.js + React)
- **Modern UI**: Beautiful, responsive interface built with Tailwind CSS
- **MetaMask Integration**: Seamless wallet connection with Ethers.js
- **Token Purchase**: Credit card to crypto conversion flow
- **Transaction History**: Complete transaction tracking and analytics
- **Admin Panel**: Token management interface for administrators
- **Real-time Balance**: Live token balance updates

### 💳 Payment Processing API
- **Stripe Integration**: Secure credit card payment processing
- **Token Issuance**: Automatic token minting after successful payments
- **RESTful API**: Comprehensive backend for all operations
- **Admin Controls**: Secure admin endpoints for token management
- **Transaction Logging**: Complete audit trail

### 🛡️ Security Features
- **Rate Limiting**: Protection against abuse
- **Input Validation**: Comprehensive data validation
- **JWT Authentication**: Secure user sessions
- **Admin API Keys**: Separate authentication for admin operations
- **Error Handling**: Robust error management and logging

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- MetaMask browser extension
- Running Ethermint node
- Stripe account (for payments)

### 1. Smart Contract Deployment

```bash
# Install dependencies
npm install

# Start Ethermint node
ethermintd start

# Deploy contract
npx hardhat run scripts/deploy.js --network ethermint
```

### 2. Frontend dApp

```bash
cd dapp-ui
npm install
npm run dev
```

Visit `http://localhost:3000`

### 3. Backend API

```bash
cd api-server
npm install
cp .env.example .env
# Configure .env file
npm run dev
```

API available at `http://localhost:3001`

## 📱 dApp Features

### 🏠 Dashboard
- Token statistics and overview
- Quick action buttons
- Contract information
- Real-time data

### 💰 Buy Tokens
- Credit card payment form
- Stripe payment processing
- Automatic token minting
- Purchase confirmation

### 📤 Transfer Tokens
- Send tokens to any address
- Real-time balance updates
- Transaction confirmation
- Transfer history

### 📊 Transaction History
- Complete transaction log
- Filter and search functionality
- Export to CSV
- Real-time status updates

### ⚙️ Admin Panel
- Mint new tokens
- Burn existing tokens
- System statistics
- Admin action logging

## 🔧 Configuration

### Environment Variables

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_CONTRACT_ADDRESS=0x...
NEXT_PUBLIC_CHAIN_ID=9000
NEXT_PUBLIC_RPC_URL=http://127.0.0.1:8545
```

#### Backend (.env)
```bash
PORT=3001
RPC_URL=http://127.0.0.1:8545
CONTRACT_ADDRESS=0x...
PRIVATE_KEY=your_private_key
STRIPE_SECRET_KEY=sk_test_...
JWT_SECRET=your_jwt_secret
ADMIN_API_KEY=your_admin_key
```

## 🧪 Testing

### Smart Contract Tests
```bash
npx hardhat test
```

### Frontend Testing
```bash
cd dapp-ui
npm test
```

### API Testing
```bash
cd api-server
npm test
```

## 🚀 Production Deployment

1. Deploy smart contract to mainnet
2. Configure environment variables
3. Deploy frontend to Vercel/Netlify
4. Deploy backend to AWS/DigitalOcean
5. Set up monitoring and logging

## 📄 License

MIT License - Built with ❤️ by the DATACOIN team
