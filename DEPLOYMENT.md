# DATACOIN ERC (DTCERC) Token Deployment Guide

## 📋 Overview

This guide will help you deploy the DATACOIN ERC token with the following specifications:
- **Name**: DATACOIN ERC
- **Symbol**: DTCERC
- **Type**: ERC20 Token with Mint/Burn capabilities
- **Decimals**: 18
- **Initial Supply**: 0 (tokens are minted as needed)

## 🚀 Quick Start

### 1. Compile the Contract
```bash
npm run compile
```

### 2. Deploy to Local Network
```bash
# Start local Hardhat node (in separate terminal)
npm run node

# Deploy to local network
npm run deploy:local
```

### 3. Deploy to Your Network
```bash
# Deploy to configured network
npm run deploy
```

### 4. Mint Test Tokens
```bash
# Mint tokens for testing
npm run mint:local
```

### 5. Verify Contract
```bash
# Verify contract is working
npm run verify:local
```

## 🔧 Detailed Steps

### Step 1: Prepare Environment

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Network** (in `hardhat.config.js`)
   ```javascript
   networks: {
     localhost: {
       url: "http://127.0.0.1:8545"
     },
     // Add your custom network here
   }
   ```

### Step 2: Deploy Contract

1. **Compile Contract**
   ```bash
   npm run compile
   ```

2. **Deploy Contract**
   ```bash
   # For local testing
   npm run deploy:local

   # For your network
   npm run deploy
   ```

3. **Note the Contract Address**
   The deployment script will output:
   ```
   📄 Contract Address: 0x...
   🔗 Transaction Hash: 0x...
   ```

### Step 3: Mint Initial Tokens

1. **Update Contract Address** in `scripts/mint-tokens.js`:
   ```javascript
   const CONTRACT_ADDRESS = "0xYourContractAddress"; // Update this!
   ```

2. **Mint Tokens**
   ```bash
   npm run mint:local
   ```

### Step 4: Update Frontend

1. **Update `.env.local`** in your dApp:
   ```env
   NEXT_PUBLIC_CONTRACT_ADDRESS=0xYourContractAddress
   NEXT_PUBLIC_TOKEN_NAME=DATACOIN ERC
   NEXT_PUBLIC_TOKEN_SYMBOL=DTCERC
   ```

2. **Restart Frontend**
   ```bash
   cd dapp-ui
   npm run dev
   ```

## 📱 MetaMask Integration

### Add Token to MetaMask

1. Open MetaMask
2. Click "Import tokens"
3. Enter:
   - **Contract Address**: `0xYourContractAddress`
   - **Token Symbol**: `DTCERC`
   - **Decimals**: `18`

### Add Network to MetaMask (if needed)

1. Open MetaMask
2. Click "Add Network"
3. Enter your network details:
   - **Network Name**: DATACOIN Network
   - **RPC URL**: Your RPC URL
   - **Chain ID**: Your Chain ID
   - **Currency Symbol**: DTC

## 🛠️ Available Scripts

| Command | Description |
|---------|-------------|
| `npm run compile` | Compile smart contracts |
| `npm run deploy` | Deploy to configured network |
| `npm run deploy:ethermint` | Deploy to ethermint Hardhat network |
| `npm run mint` | Mint tokens for testing |
| `npm run mint:ethermint` | Mint tokens on ethermint network |
| `npm run verify` | Verify contract functionality |
| `npm run verify:local` | Verify on local network |
| `npm run node` | Start local Hardhat node |

## 🔍 Troubleshooting

### Common Issues

1. **"Contract not deployed"**
   - Check contract address is correct
   - Verify you're on the right network
   - Run `npm run verify:local` to check

2. **"Insufficient funds"**
   - Ensure deployer account has enough ETH/native tokens
   - Check gas price settings

3. **"Transaction failed"**
   - Check gas limit (default: 2,000,000)
   - Verify network connection
   - Check if contract already exists at address

### Verification Steps

1. **Check Contract Deployment**
   ```bash
   npm run verify:local
   ```

2. **Check Token Balance**
   - Use MetaMask to view DTCERC balance
   - Or check via frontend dApp

3. **Test Token Transfer**
   - Use frontend to send tokens
   - Verify transaction on block explorer

## 📊 Contract Features

### Owner Functions
- `mint(address to, uint256 amount)` - Mint new tokens
- `burn(uint256 amount)` - Burn tokens from caller
- `burnFrom(address account, uint256 amount)` - Burn tokens from account

### Standard ERC20 Functions
- `transfer(address to, uint256 amount)` - Transfer tokens
- `approve(address spender, uint256 amount)` - Approve spending
- `transferFrom(address from, address to, uint256 amount)` - Transfer from approved account

### View Functions
- `name()` - Returns "DATACOIN ERC"
- `symbol()` - Returns "DTCERC"
- `decimals()` - Returns 18
- `totalSupply()` - Returns total token supply
- `balanceOf(address account)` - Returns account balance

## 🎯 Next Steps

After successful deployment:

1. ✅ Update frontend configuration
2. ✅ Add token to MetaMask
3. ✅ Mint test tokens
4. ✅ Test token transfers
5. ✅ Verify all functionality works
6. 🚀 Start using your DTCERC token!

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all configuration files
3. Ensure network connectivity
4. Check gas fees and account balances
